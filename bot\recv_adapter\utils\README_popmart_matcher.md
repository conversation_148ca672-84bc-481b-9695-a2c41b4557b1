# 泡泡玛特商品匹配器使用指南

## 概述

针对你提供的真实泡泡玛特商品数据和文本，我创建了专用的商品匹配器，能够准确识别复杂的商品表达式。

## 支持的表达式类型

### 1. 复杂系列衍生品表达
```
DIMOO心动特调系列-衍生品【软脸毛绒钥匙扣盲盒/水晶球/咖啡杯/盲盒亚克力冰箱贴夹子/盲盒手机挂链/香包挂件套装】
```
- 自动识别中括号内的多个子商品
- 支持斜杠分隔的商品列表

### 2. 系列-子商品表达
```
DIMOO心动特调系列-搪胶毛绒吊卡
CRYBABY悲伤俱乐部系列-硅胶毛绒耳机包
```
- 识别系列名称和具体子商品的关系

### 3. 分号分隔的多商品列表
```
迪士尼公主创想世界系列手办；KUBO日落之城系列手办；浪漫指尖系列4场景手办；
```
- 自动分割并识别每个商品

### 4. 简单列举
```
1️⃣CRYBABY悲伤俱乐部系列-硅胶毛绒耳机包
2️⃣HACIPUPU史迪奇1/8可动人偶
```
- 支持编号前缀的商品列表

## 数据格式

### 输入格式（你的原始数据）
```python
raw_products = [
    "迪士尼公主创想世界系列手办",
    "DIMOO心动特调系列-搪胶毛绒吊卡，DIMOO心动特调系列",
    "DIMOO心动特调系列-软脸毛绒钥匙扣盲盒，DIMOO心动特调系列，钥匙扣",
    # ... 更多商品
]
```

### 转换后的格式
```python
product_catalog = [
    {
        "id": 1,
        "name": "迪士尼公主创想世界系列手办",
        "series": "",
        "sub_product": ""
    },
    {
        "id": 5,
        "name": "DIMOO心动特调系列-搪胶毛绒吊卡",
        "series": "DIMOO心动特调系列",
        "sub_product": "搪胶毛绒吊卡"
    },
    # ... 更多商品
]
```

### 返回格式
```python
[
    {
        "商品ID": 5,
        "商品名称": "DIMOO心动特调系列-搪胶毛绒吊卡",
        "匹配信息": {
            "匹配模式": "DIMOO心动特调系列衍生品搪胶毛绒吊卡",
            "原始文本中的位置": [15, 45],
            "错字信息列表": []
        }
    }
]
```

## 使用方法

### 方法1：直接使用匹配器
```python
from utils.popmart_product_matcher import PopmartProductMatcher, convert_popmart_data

# 转换你的原始数据
raw_products = ["迪士尼公主创想世界系列手办", "DIMOO心动特调系列-搪胶毛绒吊卡，DIMOO心动特调系列"]
product_catalog = convert_popmart_data(raw_products)

# 创建匹配器
matcher = PopmartProductMatcher(product_catalog)

# 匹配文本
text = "DIMOO心动特调系列-衍生品【软脸毛绒钥匙扣盲盒/水晶球/咖啡杯】"
results = matcher.match(text, max_edits=1)

for result in results:
    print(f"匹配到商品: {result['商品名称']}")
```

### 方法2：使用全局匹配器
```python
from utils.popmart_product_matcher import set_popmart_matcher, match_popmart_products

# 设置全局匹配器（一次性）
set_popmart_matcher(product_catalog)

# 之后只需传入文本
results = match_popmart_products("你的文本内容", max_edits=1)
```

### 方法3：在 MessageHandler 中使用（已集成）
```python
from handlers.message_handler import MessageHandler
from utils.popmart_product_matcher import convert_popmart_data

# 创建消息处理器
handler = MessageHandler()

# 设置商品目录（使用你的原始数据）
raw_products = [
    "迪士尼公主创想世界系列手办",
    "DIMOO心动特调系列-搪胶毛绒吊卡，DIMOO心动特调系列",
    # ... 你的完整商品列表
]
handler.product_catalog = convert_popmart_data(raw_products)

# 之后处理消息时会自动进行商品匹配
# 匹配结果会放在转发数据的 Data.ProductMatches 字段中
```

## 性能特点

1. **预构建索引**：商品列表固定后，一次性构建所有索引
2. **专用正则**：针对泡泡玛特的表达式优化
3. **智能去重**：自动处理重叠匹配，优先选择最长最准确的结果
4. **容错匹配**：支持1个错别字的模糊匹配
5. **位置精确**：返回原始文本中的准确字符位置

## 测试验证

运行测试文件验证匹配效果：
```bash
cd bot/recv_adapter/utils
python popmart_test.py
```

测试覆盖了你提供的所有文本场景，确保能正确识别各种复杂表达式。

## 与通用匹配器的区别

| 特性 | 通用匹配器 | 泡泡玛特专用匹配器 |
|------|------------|-------------------|
| 中括号表达式 | ❌ | ✅ |
| 系列-子商品关系 | 部分支持 | ✅ 完全支持 |
| 分号分隔列表 | ❌ | ✅ |
| 长商品名称 | ✅ | ✅ 优化 |
| 模糊匹配 | ✅ | ✅ |
| 性能 | 高 | 高（专用优化）|

## 注意事项

1. **数据格式**：确保使用 `convert_popmart_data()` 转换你的原始数据
2. **容错设置**：建议保持 `max_edits=1`，避免过度匹配
3. **系列关系**：匹配器会自动处理系列和子商品的关系
4. **回退机制**：如果专用匹配器失败，会自动回退到通用匹配器

## 扩展支持

如果有新的表达式类型，可以在 `_parse_popmart_series_expressions()` 函数中添加新的正则模式。
