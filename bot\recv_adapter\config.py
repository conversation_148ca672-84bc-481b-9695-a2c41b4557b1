"""
配置文件
"""
import os
from typing import Dict, Any
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # RabbitMQ配置
    rabbitmq_host: str = "**************"
    rabbitmq_port: int = 5672
    rabbitmq_username: str = ""
    rabbitmq_password: str = ""
    rabbitmq_vhost: str = "/"
    
    # RabbitMQ管理API配置
    rabbitmq_management_host: str = "**************"
    rabbitmq_management_port: int = 15672
    rabbitmq_management_username: str = ""
    rabbitmq_management_password: str = ""
    
    # 监听的队列
    queues: Dict[str, int] = {
        "wechatpadpro": 1,  # 版本1
        "wx_msg": 1,        # 版本1.5
        "wxapi": 2          # 版本2
    }

    # Webhook配置
    webhook_enabled: bool = True  # 是否启用webhook接收器
    webhook_host: str = "0.0.0.0"  # webhook监听地址
    webhook_port: int = 8080  # webhook监听端口
    webhook_path: str = "/webhook"  # webhook接收路径
    
    # HTTP转发配置
    forward_base_url: str = "http://**************:9312/recv"
    
    # 消息过滤配置
    filter_history_minutes: int = 1  # 过滤多少分钟前的历史消息
    
    # 去重配置
    dedup_history_size: int = 10000  # 去重历史记录大小
    
    # 定时清理配置
    cleanup_interval_minutes: int = 1  # 清理间隔（分钟）
    cleanup_queue_prefix: str = "wx_messages"  # 要清理的队列前缀
    
    # 日志配置
    log_level: str = "INFO"

    # 未处理消息日志配置
    unhandled_message_log_enabled: bool = True  # 是否启用未处理消息日志
    unhandled_message_log_filepath: str = "logs/unknown/"  # 未处理消息日志目录

    # MySQL数据库配置
    mysql_host: str = "rm-uf6psdjix97ed81r8.mysql.rds.aliyuncs.com"
    mysql_port: int = 3306
    mysql_user: str = "bot"
    mysql_password: str = "yaoboan19990312!"
    mysql_database: str = "yba_ppmt"
    mysql_charset: str = "utf8mb4"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置实例
settings = Settings()
