"""
调试中括号正则表达式匹配问题
"""

import re
from product_matcher import normalize

def debug_bracket_regex():
    print("=" * 60)
    print("调试中括号正则表达式匹配")
    print("=" * 60)
    
    # 测试文本
    test_text = """🔔发售产品：
🎈盲盒手办：迪士尼公主创想世界系列手办；KUBO日落之城系列手办；浪漫指尖系列4场景手办；
🎈大号&吊卡&BJD手办：CRYBABY 真爱之盒手办；DIMOO心动特调系列-搪胶毛绒吊卡；MOLLY你好，月亮1/8可动人偶；
🎈衍生品：DIMOO心动特调系列-衍生品【软脸毛绒钥匙扣盲盒/水晶球/咖啡杯/盲盒亚克力冰箱贴夹子/盲盒手机挂链/香包挂件套装】"""
    
    print("1. 文本内容检查")
    print("-" * 40)
    print(f"文本长度: {len(test_text)}")
    print(f"包含【: {'【' in test_text}")
    print(f"包含】: {'】' in test_text}")
    
    # 查找中括号位置
    bracket_start = test_text.find('【')
    bracket_end = test_text.find('】')
    print(f"【位置: {bracket_start}")
    print(f"】位置: {bracket_end}")
    
    if bracket_start >= 0 and bracket_end >= 0:
        bracket_content = test_text[bracket_start:bracket_end+1]
        print(f"中括号内容: {bracket_content}")
        
        # 提取前面的文本
        before_bracket = test_text[max(0, bracket_start-50):bracket_start]
        print(f"中括号前50字符: ...{before_bracket}")
    
    print()
    print("2. 正则表达式测试")
    print("-" * 40)
    
    # 当前使用的正则表达式
    bracket_pattern = re.compile(r'([^【]+?)(?:系列)?-?衍生品?【([^】]+)】')
    
    print(f"正则表达式: {bracket_pattern.pattern}")
    
    matches = list(bracket_pattern.finditer(test_text))
    print(f"匹配数量: {len(matches)}")
    
    for i, match in enumerate(matches, 1):
        print(f"匹配 {i}:")
        print(f"  完整匹配: '{match.group(0)}'")
        print(f"  系列前缀: '{match.group(1)}'")
        print(f"  项目列表: '{match.group(2)}'")
        print(f"  位置: [{match.start()}, {match.end()}]")
    
    print()
    print("3. 改进的正则表达式测试")
    print("-" * 40)
    
    # 尝试更宽松的正则表达式
    patterns_to_test = [
        r'([^【]+?)(?:系列)?-?衍生品?【([^】]+)】',  # 原始
        r'([^【\n]+?)(?:系列)?-?衍生品?【([^】]+)】',  # 不允许换行
        r'([^【\n]+?)衍生品【([^】]+)】',  # 简化版
        r'(.+?)衍生品【([^】]+)】',  # 最宽松
        r'DIMOO心动特调系列-衍生品【([^】]+)】',  # 特定匹配
    ]
    
    for i, pattern_str in enumerate(patterns_to_test, 1):
        print(f"测试模式 {i}: {pattern_str}")
        pattern = re.compile(pattern_str)
        matches = list(pattern.finditer(test_text))
        print(f"  匹配数量: {len(matches)}")
        
        for match in matches:
            print(f"  匹配: '{match.group(0)}'")
            if match.lastindex >= 2:
                print(f"    系列: '{match.group(1)}'")
                print(f"    项目: '{match.group(2)}'")
            elif match.lastindex == 1:
                print(f"    项目: '{match.group(1)}'")
        print()
    
    print("4. 手动提取测试")
    print("-" * 40)
    
    # 手动查找和提取
    if '衍生品【' in test_text:
        start_pos = test_text.find('衍生品【')
        end_pos = test_text.find('】', start_pos)
        
        if start_pos >= 0 and end_pos >= 0:
            # 提取系列前缀
            prefix_start = max(0, start_pos - 20)  # 向前查找20个字符
            prefix_text = test_text[prefix_start:start_pos]
            
            # 查找系列名称
            lines = prefix_text.split('\n')
            last_line = lines[-1] if lines else ""
            
            print(f"衍生品前的文本: '{last_line}'")
            
            # 提取项目列表
            items_text = test_text[start_pos+3:end_pos]  # 跳过'衍生品【'
            print(f"项目列表: '{items_text}'")
            
            # 分割项目
            items = re.split(r'[/／、，,]', items_text)
            print(f"分割后的项目: {items}")


if __name__ == "__main__":
    debug_bracket_regex()
