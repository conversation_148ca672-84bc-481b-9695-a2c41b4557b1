"""
测试新的正则表达式
"""

import re

def test_new_regex():
    print("=" * 60)
    print("测试新的正则表达式")
    print("=" * 60)
    
    # 测试文本
    test_text = """🔔发售产品：
🎈盲盒手办：迪士尼公主创想世界系列手办；KUBO日落之城系列手办；浪漫指尖系列4场景手办；
🎈大号&吊卡&BJD手办：CRYBABY 真爱之盒手办；DIMOO心动特调系列-搪胶毛绒吊卡；MOLLY你好，月亮1/8可动人偶；
🎈衍生品：DIMOO心动特调系列-衍生品【软脸毛绒钥匙扣盲盒/水晶球/咖啡杯/盲盒亚克力冰箱贴夹子/盲盒手机挂链/香包挂件套装】"""
    
    # 新的正则表达式
    new_pattern = re.compile(r'([A-Za-z0-9\u4e00-\u9fff]+系列)-?衍生品?【([^】]+)】')
    
    print("新正则表达式:", new_pattern.pattern)
    
    matches = list(new_pattern.finditer(test_text))
    print(f"匹配数量: {len(matches)}")
    
    for i, match in enumerate(matches, 1):
        print(f"匹配 {i}:")
        print(f"  完整匹配: '{match.group(0)}'")
        print(f"  系列前缀: '{match.group(1)}'")
        print(f"  项目列表: '{match.group(2)}'")
        print(f"  位置: [{match.start()}, {match.end()}]")
        
        # 测试系列前缀处理
        series_prefix = match.group(1).strip()
        print(f"  处理后系列前缀: '{series_prefix}'")
        
        from product_matcher import normalize
        series_norm = normalize(series_prefix)
        print(f"  规范化系列前缀: '{series_norm}'")
        
        # 测试项目分割
        items_str = match.group(2)
        items = re.split(r'[/／、，,]', items_str)
        print(f"  分割后项目: {items}")
        
        for j, item in enumerate(items, 1):
            item = item.strip()
            if item:
                item_norm = normalize(item)
                print(f"    项目{j}: '{item}' -> '{item_norm}'")


if __name__ == "__main__":
    test_new_regex()
