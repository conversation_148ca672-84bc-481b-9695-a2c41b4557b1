"""
商品匹配结果订阅示例
带TTL/长度/溢出/DLX设置
"""
import aio_pika
import json
import asyncio
from loguru import logger

# 使用你的RabbitMQ配置
AMQP_URL = "amqp://user:pass@139.224.33.158:5672/"  # 替换为实际的用户名密码

async def consume_product_matches(app_name: str = "appX"):
    """
    订阅商品匹配结果
    
    Args:
        app_name: 应用名称，用于队列命名
    """
    conn = await aio_pika.connect_robust(AMQP_URL)
    ch = await conn.channel()

    # 业务交换机
    ex = await ch.declare_exchange("events.product-matches", aio_pika.ExchangeType.TOPIC, durable=True)

    # 死信交换机与队列
    dlx = await ch.declare_exchange("dlx.product-matches", aio_pika.ExchangeType.DIRECT, durable=True)
    dlq = await ch.declare_queue("dlq.product-matches", durable=True)
    await dlq.bind(dlx, routing_key="dlq.product-matches")

    # 订阅队列（带TTL/长度/溢出/DLX设置）
    queue_name = f"{app_name}.product-matches"
    args = {
        "x-message-ttl": 300000,                     # 消息存活5分钟
        "x-max-length": 50000,                       # 最多5万条消息
        "x-overflow": "drop-head",                   # 超限丢弃最老消息
        "x-dead-letter-exchange": "dlx.product-matches",
        "x-dead-letter-routing-key": "dlq.product-matches",
        "x-expires": 7 * 24 * 3600 * 1000          # 队列闲置7天自动删除
    }
    
    q = await ch.declare_queue(queue_name, durable=True, arguments=args)
    
    # 绑定到交换机，订阅所有店铺的商品匹配结果
    await q.bind(ex, routing_key="product.matches.store.*")

    logger.info(f"Started consuming product matches on queue: {queue_name}")

    async with q.iterator() as it:
        async for m in it:
            async with m.process():
                try:
                    event = json.loads(m.body.decode("utf-8"))
                    
                    # 处理商品匹配结果
                    ts = event.get("ts")
                    msg_id = event.get("msg_id")
                    store = event.get("store", {})
                    products = event.get("products", [])
                    
                    store_id = store.get("id")
                    store_name = store.get("name")
                    
                    logger.info(f"Received product matches at {ts}")
                    logger.info(f"Message ID: {msg_id}")
                    logger.info(f"Store: {store_name} (ID: {store_id})")
                    logger.info(f"Products count: {len(products)}")
                    
                    for product in products:
                        product_id = product.get("id")
                        product_name = product.get("name")
                        logger.info(f"  - {product_name} (ID: {product_id})")
                    
                    # 在这里添加你的业务逻辑
                    # 例如：更新库存、发送通知、记录统计等
                    await process_product_matches(store, products, ts, msg_id)
                    
                except Exception as e:
                    logger.error(f"Error processing product match event: {e}")


async def process_product_matches(store: dict, products: list, ts: str, msg_id: int):
    """
    处理商品匹配结果的业务逻辑
    
    Args:
        store: 店铺信息
        products: 商品列表
        ts: 时间戳
        msg_id: 消息ID
    """
    # 示例业务逻辑
    logger.info(f"Processing {len(products)} products for store {store['name']}")
    
    # 这里可以添加你的具体业务逻辑，例如：
    # 1. 更新商品库存
    # 2. 发送库存预警
    # 3. 记录销售统计
    # 4. 触发补货流程
    # 5. 发送通知给相关人员
    
    for product in products:
        # 模拟处理每个商品
        logger.debug(f"Processing product: {product['name']} (ID: {product['id']})")
        # await update_inventory(product['id'])
        # await check_stock_alert(product['id'])
        pass


async def main():
    """主函数"""
    try:
        # 启动订阅者
        await consume_product_matches("myapp")
    except KeyboardInterrupt:
        logger.info("Subscriber stopped by user")
    except Exception as e:
        logger.error(f"Subscriber error: {e}")


if __name__ == "__main__":
    # 配置日志
    logger.add("logs/subscriber.log", rotation="1 day", retention="7 days")
    
    # 运行订阅者
    asyncio.run(main())
