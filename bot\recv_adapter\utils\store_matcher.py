"""
店铺匹配器模块

提供基于店铺名称的高性能匹配功能，支持：
1. 精确匹配
2. 模糊匹配（编辑距离）
3. 别名匹配
4. group_id内存索引快速查找

参考商品匹配器的实现方式
"""
import re
from typing import List, Dict, Any, Optional, Set, Tuple
from collections import defaultdict
from loguru import logger


class StoreMatcher:
    """店铺匹配器"""
    
    def __init__(self, store_catalog: List[Dict[str, Any]]):
        """
        初始化店铺匹配器
        
        Args:
            store_catalog: 店铺数据列表，每个元素包含：
                - id: 店铺ID
                - name: 店铺名称
                - match_names: 匹配名称列表
                - group_ids: 群ID列表
                - location: 地区
                - opening: 开业状态
        """
        self.stores = store_catalog
        self.store_dict = {store['id']: store for store in store_catalog}
        
        # 构建group_id内存索引
        self.group_id_index = self._build_group_id_index()
        
        # 构建名称索引
        self.name_index = self._build_name_index()
        
        logger.info(f"店铺匹配器初始化完成，加载 {len(self.stores)} 个店铺，"
                   f"group_id索引 {len(self.group_id_index)} 个群")
    
    def _build_group_id_index(self) -> Dict[str, Dict[str, Any]]:
        """构建group_id到店铺的内存索引（只包含开业的店铺）"""
        index = {}
        for store in self.stores:
            # 只索引开业的店铺
            if store.get('opening', True):
                for group_id in store.get('group_ids', []):
                    if group_id:
                        index[group_id] = store
        return index
    
    def _build_name_index(self) -> Dict[str, List[Dict[str, Any]]]:
        """构建名称到店铺的索引（只包含开业的店铺）"""
        index = defaultdict(list)

        for store in self.stores:
            # 只索引开业的店铺
            if store.get('opening', True):
                # 主名称
                if store.get('name'):
                    normalized_name = self._normalize_text(store['name'])
                    index[normalized_name].append(store)

                # 匹配名称
                for match_name in store.get('match_names', []):
                    if match_name:
                        normalized_name = self._normalize_text(match_name)
                        index[normalized_name].append(store)

        return dict(index)
    
    def _normalize_text(self, text: str) -> str:
        """标准化文本"""
        if not text:
            return ""
        # 去除空格、标点符号，转小写
        return re.sub(r'[^\w\u4e00-\u9fff]', '', text.lower())
    
    def get_store_by_group_id(self, group_id: str) -> Optional[Dict[str, Any]]:
        """
        通过group_id快速查找店铺（内存索引）
        
        Args:
            group_id: 群ID
            
        Returns:
            店铺信息字典，如果不存在则返回None
        """
        return self.group_id_index.get(group_id)
    
    def match_stores_by_name(self, text: str, max_edits: int = 0) -> Optional[Dict[str, Any]]:
        """
        通过店铺名称匹配店铺（只返回一个最精确的结果）

        Args:
            text: 待匹配文本
            max_edits: 最大编辑距离，默认为0（不允许错字）

        Returns:
            最佳匹配的店铺信息字典，如果没有匹配则返回None
        """
        if not text:
            return None

        normalized_text = self._normalize_text(text)

        # 1. 精确匹配优先
        exact_matches = self._exact_match(normalized_text)
        if exact_matches:
            # 如果有多个精确匹配，返回第一个（通常是主名称匹配）
            store = exact_matches[0]
            return {
                "店铺ID": store['id'],
                "店铺名称": store['name'],
                "地区": store.get('location', ''),
                "开业状态": store.get('opening', True),
                "群组列表": store.get('group_ids', []),
                "匹配信息": {
                    "匹配模式": "精确匹配",
                    "匹配文本": text,
                    "置信度": 1.0
                }
            }

        # 2. 如果允许模糊匹配且max_edits > 0
        if max_edits > 0:
            fuzzy_matches = self._fuzzy_match(normalized_text, max_edits)
            if fuzzy_matches:
                # 返回置信度最高的匹配结果
                store, confidence = fuzzy_matches[0]
                return {
                    "店铺ID": store['id'],
                    "店铺名称": store['name'],
                    "地区": store.get('location', ''),
                    "开业状态": store.get('opening', True),
                    "群组列表": store.get('group_ids', []),
                    "匹配信息": {
                        "匹配模式": "模糊匹配",
                        "匹配文本": text,
                        "置信度": confidence
                    }
                }

        return None
    
    def _exact_match(self, normalized_text: str) -> List[Dict[str, Any]]:
        """精确匹配"""
        return self.name_index.get(normalized_text, [])
    
    def _fuzzy_match(self, normalized_text: str, max_edits: int) -> List[Tuple[Dict[str, Any], float]]:
        """模糊匹配"""
        matches = []
        
        for name, stores in self.name_index.items():
            distance = self._edit_distance(normalized_text, name)
            if distance <= max_edits:
                confidence = 1.0 - (distance / max(len(normalized_text), len(name)))
                for store in stores:
                    matches.append((store, confidence))
        
        # 按置信度排序
        matches.sort(key=lambda x: x[1], reverse=True)
        return matches
    
    def _edit_distance(self, s1: str, s2: str) -> int:
        """计算编辑距离"""
        if len(s1) < len(s2):
            s1, s2 = s2, s1
        
        if len(s2) == 0:
            return len(s1)
        
        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]


# 全局店铺匹配器实例
_global_store_matcher: Optional[StoreMatcher] = None


def set_global_store_matcher(store_catalog: List[Dict[str, Any]]):
    """设置全局店铺匹配器"""
    global _global_store_matcher
    _global_store_matcher = StoreMatcher(store_catalog)


async def init_store_matcher_from_database():
    """从数据库初始化全局店铺匹配器"""
    from utils.database import get_store_database

    store_db = get_store_database()
    if not store_db:
        raise RuntimeError("数据库未初始化，请先调用 init_database()")

    try:
        # 从数据库获取店铺数据
        stores = await store_db.get_all_stores()

        # 设置全局匹配器
        set_global_store_matcher(stores)

        logger.info(f"成功从数据库加载 {len(stores)} 个店铺到匹配器")

    except Exception as e:
        logger.error(f"从数据库初始化店铺匹配器失败: {e}")
        raise


def get_global_store_matcher() -> Optional[StoreMatcher]:
    """获取全局店铺匹配器"""
    return _global_store_matcher


def get_store_by_group_id(group_id: str) -> Optional[Dict[str, Any]]:
    """
    使用全局匹配器通过group_id查找店铺（内存索引，高性能）
    
    Args:
        group_id: 群ID
        
    Returns:
        店铺信息字典，如果不存在则返回None
    """
    matcher = get_global_store_matcher()
    if matcher is None:
        return None
    return matcher.get_store_by_group_id(group_id)


def match_stores_by_name(text: str, max_edits: int = 0) -> Optional[Dict[str, Any]]:
    """
    使用全局匹配器进行店铺名称匹配（只返回一个最精确的结果）

    Args:
        text: 待匹配文本
        max_edits: 最大编辑距离，默认为0（不允许错字）

    Returns:
        最佳匹配的店铺信息字典，如果没有匹配则返回None
    """
    matcher = get_global_store_matcher()
    if matcher is None:
        return None
    return matcher.match_stores_by_name(text, max_edits)


__all__ = [
    'StoreMatcher',
    'set_global_store_matcher',
    'init_store_matcher_from_database',
    'get_global_store_matcher',
    'get_store_by_group_id',
    'match_stores_by_name'
]
