"""
系列与组合命中处理：
- 解析诸如：
  1) ABC\nABC-A\nABC-B
  2) ABC，ABC-A，ABC-B
  3) ABC-A/B（没有ABC）
  4) ABC/A/B（有ABC）
  5) ABC及其衍生品A和B
- 将命中的“主名 + 变体缩写”统一展开为具体商品名（例如 ABC + A/B -> ABC-A, ABC-B）

对外API：
- expand_series_matches(text, series_patterns)
  输入：
    text: 原始文本
    series_patterns: { 'ABC': {'prefix': 'ABC', 'variants': {'A': 'ABC-A', 'B': 'ABC-B'}, 'full': ['ABC','ABC-A','ABC-B']}}
  输出：
    列表： [{'series': 'ABC', 'expanded_products': ['ABC-A','ABC-B'], 'spans': [...原文span...]}]

注意：该模块只做“系列风格的表达解析与展开”，并不负责编辑距离模糊匹配。
"""
from __future__ import annotations

from typing import Dict, Any, List, Tuple
import re

# 简单中文/英文分隔符统一
SEP_RE = re.compile(r"[\s,，、；;\/]+")

# 识别“及其衍生品”类表达
DERIV_RE = re.compile(r"(?P<prefix>[A-Za-z0-9\u4e00-\u9fff]+)\s*及其(?:[特装]*|[衍生]*|[系列]*)*\s*[产周物配品]*\s*(?P<rest>.+)")
AND_RE = re.compile(r"[和及、,，]\s*")


def expand_series_matches(text: str, series_patterns: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
    results: List[Dict[str, Any]] = []

    # 处理 3) ABC-A/B 这种：带“-”连接主前缀和多变体
    dash_groups = re.finditer(r"(?P<prefix>[A-Za-z0-9\u4e00-\u9fff]+)-(?P<variants>[A-Za-z0-9]+(?:\/[A-Za-z0-9]+)+)", text)
    for m in dash_groups:
        prefix = m.group('prefix')
        variants = m.group('variants').split('/')
        prefix_key = _match_series_key(prefix, series_patterns)
        if not prefix_key:
            continue
        expanded, spans = _expand(prefix_key, variants, series_patterns)
        if expanded:
            results.append({'series': prefix_key, 'expanded_products': expanded, 'spans': [(m.start(), m.end())]})

    # 处理 4) ABC/A/B （有ABC）这种：主名前缀 + 斜杠多个项
    slash_groups = re.finditer(r"(?P<prefix>[A-Za-z0-9\u4e00-\u9fff]+)\/(?P<rest>[A-Za-z0-9]+(?:\/[A-Za-z0-9]+)+)", text)
    for m in slash_groups:
        prefix = m.group('prefix')
        variants = m.group('rest').split('/')
        prefix_key = _match_series_key(prefix, series_patterns)
        if not prefix_key:
            continue
        expanded, spans = _expand(prefix_key, variants, series_patterns)
        if expanded:
            results.append({'series': prefix_key, 'expanded_products': expanded, 'spans': [(m.start(), m.end())]})

    # 处理 5) ABC及其衍生品A和B
    for m in DERIV_RE.finditer(text):
        prefix = m.group('prefix')
        rest = m.group('rest')
        prefix_key = _match_series_key(prefix, series_patterns)
        if not prefix_key:
            continue
        # 拆分 A 和 B
        tokens = [t for t in AND_RE.split(rest) if t]
        # 再细分可能包含的分隔：A/B 或 A, B
        variants: List[str] = []
        for tok in tokens:
            variants.extend([x for x in SEP_RE.split(tok) if x])
        expanded, spans = _expand(prefix_key, variants, series_patterns)
        if expanded:
            results.append({'series': prefix_key, 'expanded_products': expanded, 'spans': [(m.start(), m.end())]})

    return results


def _match_series_key(prefix: str, series_patterns: Dict[str, Dict[str, Any]]) -> str:
    # 简单大小写与全角归一
    p = _to_half_width(prefix).lower()
    for key in series_patterns.keys():
        if _to_half_width(key).lower() == p:
            return key
    return ''


def _expand(series_key: str, variants: List[str], patterns: Dict[str, Dict[str, Any]]):
    info = patterns.get(series_key, {})
    variant_map = info.get('variants', {})
    expanded: List[str] = []
    for v in variants:
        vv = _to_half_width(v).strip()
        name = variant_map.get(vv)
        if name and name not in expanded:
            expanded.append(name)
    return expanded, []


def _to_half_width(s: str) -> str:
    out = []
    for ch in s:
        code = ord(ch)
        if code == 0x3000:
            code = 32
        elif 0xFF01 <= code <= 0xFF5E:
            code -= 0xFEE0
        out.append(chr(code))
    return ''.join(out)

