"""
安装数据库集成所需的依赖项
"""
import subprocess
import sys
from pathlib import Path


def install_requirements():
    """安装requirements.txt中的依赖项"""
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ requirements.txt 文件不存在")
        return False
    
    try:
        print("📦 正在安装依赖项...")
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", "-r", str(requirements_file)],
            capture_output=True,
            text=True,
            check=True
        )
        
        print("✅ 依赖项安装成功")
        print(result.stdout)
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖项安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False


def check_dependencies():
    """检查关键依赖项是否已安装"""
    required_packages = [
        "aiomysql",
        "aiohttp", 
        "loguru",
        "pydantic"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    return len(missing_packages) == 0


def main():
    """主函数"""
    print("🚀 数据库集成依赖项安装程序")
    print("=" * 50)
    
    # 检查当前依赖状态
    print("检查当前依赖项状态...")
    if check_dependencies():
        print("✅ 所有依赖项都已安装")
        return
    
    # 安装依赖项
    print("\n开始安装缺失的依赖项...")
    if install_requirements():
        print("\n重新检查依赖项状态...")
        if check_dependencies():
            print("🎉 所有依赖项安装完成！")
        else:
            print("⚠️ 部分依赖项可能安装失败，请手动检查")
    else:
        print("❌ 依赖项安装失败")
        print("\n请尝试手动安装:")
        print("pip install -r requirements.txt")


if __name__ == "__main__":
    main()
