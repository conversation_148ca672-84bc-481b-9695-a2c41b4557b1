"""
增强版商品匹配器测试

测试合并了泡泡玛特解析器的 product_matcher_v2.py
"""

from product_matcher_v2 import ProductMatcher, convert_popmart_data

def test_enhanced_matcher():
    print("=" * 60)
    print("增强版商品匹配器测试")
    print("=" * 60)
    
    # 测试数据：你提供的泡泡玛特商品
    popmart_raw = [
        "迪士尼公主创想世界系列手办",
        "KUBO日落之城系列手办", 
        "浪漫指尖系列4场景手办",
        "CRYBABY 真爱之盒手办",
        "DIMOO心动特调系列-搪胶毛绒吊卡，DIMOO心动特调系列",
        "MOLLY你好，月亮1/8可动人偶",
        "DIMOO心动特调系列-软脸毛绒钥匙扣盲盒，DIMOO心动特调系列，钥匙扣",
        "DIMOO心动特调系列-水晶球，DIMOO心动特调系列，水晶球",
        "DIMOO心动特调系列-咖啡杯，DIMOO心动特调系列，咖啡杯",
        "DIMOO心动特调系列-盲盒亚克力冰箱贴夹子，DIMOO心动特调系列，冰箱贴",
        "DIMOO心动特调系列-盲盒手机挂链，DIMOO心动特调系列，手机链",
        "DIMOO心动特调系列-香包挂件套装，DIMOO心动特调系列，香包",
        "CRYBABY悲伤俱乐部系列-硅胶毛绒耳机包，CRYBABY悲伤俱乐部系列，耳机包",
        "HACIPUPU史迪奇1/8可动人偶",
        "CRYBABY SHINY SHINY系列-卡套盲盒，CRYBABY SHINY SHINY系列，卡套",
        "Hirono 钢琴师手办"
    ]
    
    print("1. 数据转换测试")
    print("-" * 40)
    
    # 转换数据格式
    product_catalog = convert_popmart_data(popmart_raw)
    
    print("转换后的商品目录（前3个）：")
    for i, product in enumerate(product_catalog[:3]):
        print(f"{i+1}. ID:{product['id']} 名称:{product['name']}")
        print(f"   匹配名称:{product['match_names']}")
        if product['series']['key']:
            print(f"   系列:{product['series']['key']}")
        if product['series']['variant']:
            print(f"   变体:{product['series']['variant']}")
        print()
    
    # 创建匹配器
    matcher = ProductMatcher(product_catalog)
    
    print("2. 中括号衍生品表达式测试")
    print("-" * 40)
    
    test_text1 = "DIMOO心动特调系列-衍生品【软脸毛绒钥匙扣盲盒/水晶球/咖啡杯/盲盒亚克力冰箱贴夹子/盲盒手机挂链/香包挂件套装】"
    results1 = matcher.match(test_text1, max_edits=1)
    
    print(f"文本: {test_text1}")
    print(f"匹配到 {len(results1)} 个商品:")
    for result in results1:
        print(f"  - 商品ID: {result['商品ID']}")
        print(f"    商品名称: {result['商品名称']}")
        print(f"    匹配模式: {result['匹配信息']['匹配模式']}")
        print(f"    位置: {result['匹配信息']['原始文本中的位置']}")
    print()
    
    print("3. 分号分隔列表测试")
    print("-" * 40)
    
    test_text2 = "迪士尼公主创想世界系列手办；KUBO日落之城系列手办；浪漫指尖系列4场景手办；"
    results2 = matcher.match(test_text2, max_edits=1)
    
    print(f"文本: {test_text2}")
    print(f"匹配到 {len(results2)} 个商品:")
    for result in results2:
        print(f"  - 商品ID: {result['商品ID']}")
        print(f"    商品名称: {result['商品名称']}")
        print(f"    匹配模式: {result['匹配信息']['匹配模式']}")
        print(f"    位置: {result['匹配信息']['原始文本中的位置']}")
    print()
    
    print("4. 系列-子商品表达测试")
    print("-" * 40)
    
    test_text3 = "DIMOO心动特调系列-搪胶毛绒吊卡；MOLLY你好,月亮1/8可动人偶；DIMOO心动特调系列-水晶球"
    results3 = matcher.match(test_text3, max_edits=1)
    
    print(f"文本: {test_text3}")
    print(f"匹配到 {len(results3)} 个商品:")
    for result in results3:
        print(f"  - 商品ID: {result['商品ID']}")
        print(f"    商品名称: {result['商品名称']}")
        print(f"    匹配模式: {result['匹配信息']['匹配模式']}")
        print(f"    位置: {result['匹配信息']['原始文本中的位置']}")
    print()
    
    print("5. 复杂混合文本测试")
    print("-" * 40)
    
    # 你提供的真实文本示例
    test_text4 = """🔔发售产品：
🎈盲盒手办：迪士尼公主创想世界系列手办；KUBO日落之城系列手办；浪漫指尖系列4场景手办；
🎈大号&吊卡&BJD手办：CRYBABY 真爱之盒手办；DIMOO心动特调系列-搪胶毛绒吊卡；MOLLY你好，月亮1/8可动人偶；
🎈衍生品：DIMOO心动特调系列-衍生品【软脸毛绒钥匙扣盲盒/水晶球/咖啡杯/盲盒亚克力冰箱贴夹子/盲盒手机挂链/香包挂件套装】"""
    
    results4 = matcher.match(test_text4, max_edits=1)
    
    print(f"文本: {test_text4[:100]}...")
    print(f"匹配到 {len(results4)} 个商品:")
    for result in results4:
        print(f"  - 商品ID: {result['商品ID']}")
        print(f"    商品名称: {result['商品名称']}")
        print(f"    匹配模式: {result['匹配信息']['匹配模式'][:30]}...")
    print()
    
    print("6. 模糊匹配测试")
    print("-" * 40)
    
    # 测试错别字
    test_text5 = "DIMOO心动特调系列-搪胶毛绒掉卡"  # "吊卡"写成"掉卡"
    results5 = matcher.match(test_text5, max_edits=1)
    
    print(f"文本: {test_text5}")
    print(f"匹配到 {len(results5)} 个商品:")
    for result in results5:
        print(f"  - 商品ID: {result['商品ID']}")
        print(f"    商品名称: {result['商品名称']}")
        print(f"    匹配模式: {result['匹配信息']['匹配模式']}")
        if result['匹配信息']['错字信息列表']:
            print(f"    错字位置: {result['匹配信息']['错字信息列表']}")
    print()
    
    print("7. 编号列表测试")
    print("-" * 40)
    
    test_text6 = """1️⃣CRYBABY悲伤俱乐部系列-硅胶毛绒耳机包(线上线下同步售卖）
2️⃣HACIPUPU史迪奇1/8可动人偶（仅线上小程序售卖，不支持代取❗️）
3️⃣CRYBABY SHINY SHINY系列-卡套盲盒（仅线上小程序售卖，不支持代取❗️）
4️⃣Hirono 钢琴师手办"""
    
    results6 = matcher.match(test_text6, max_edits=1)
    
    print(f"文本: {test_text6}")
    print(f"匹配到 {len(results6)} 个商品:")
    for result in results6:
        print(f"  - 商品ID: {result['商品ID']}")
        print(f"    商品名称: {result['商品名称']}")
        print(f"    匹配模式: {result['匹配信息']['匹配模式']}")
    print()
    
    print("=" * 60)
    print("测试总结")
    print("=" * 60)
    print("✅ 支持泡泡玛特原始数据格式转换")
    print("✅ 中括号衍生品表达式：【软脸毛绒钥匙扣盲盒/水晶球/咖啡杯】")
    print("✅ 系列-子商品表达式：DIMOO心动特调系列-搪胶毛绒吊卡")
    print("✅ 分号分隔列表：商品A；商品B；商品C")
    print("✅ 简单变体表达式：ABC-A/B, ABC/A/B")
    print("✅ 自然语言表达式：ABC及其衍生品A和B")
    print("✅ 模糊匹配和错字纠正")
    print("✅ 复杂混合文本处理")
    print("✅ 智能去重和优先级排序")
    print()
    print("🎯 增强版匹配器已成功整合泡泡玛特解析功能！")


if __name__ == "__main__":
    test_enhanced_matcher()
