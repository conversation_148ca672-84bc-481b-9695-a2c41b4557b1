# 数据库集成和商品/店铺匹配功能

本文档介绍如何使用新增的数据库读写功能、商品匹配解析器和店铺匹配功能。

## 功能概述

### 1. 数据库连接模块 (`utils/database.py`)
- 提供MySQL数据库连接池管理
- 支持异步操作
- 自动处理连接的创建和释放
- 提供商品数据的CRUD操作
- 提供店铺数据的CRUD操作，支持通过group_id查询（使用多值索引）

### 2. 商品匹配器增强 (`utils/product_matcher.py`)
- 支持从数据库动态加载商品数据
- 高性能的商品名称匹配算法
- 支持精确匹配、模糊匹配和系列表达式匹配
- 自动处理错别字和别名匹配

### 3. 店铺匹配器 (`utils/store_matcher.py`)
- 支持从数据库动态加载店铺数据（仅加载开业的店铺）
- 提供group_id内存索引，实现高性能群组到店铺的映射
- 店铺名称匹配：每次只返回一个最精确的结果
- 默认不允许错字（max_edits=0），确保匹配准确性
- 启动时一次性加载所有数据到内存，减少数据库调用

### 4. 消息处理器集成 (`handlers/message_handler.py`)
- 在文本消息处理中自动识别商品
- 通过group_id快速查找对应店铺信息
- 支持店铺名称匹配
- 将识别结果添加到转发消息中
- 支持实时商品和店铺匹配

## 数据库配置

数据库配置已添加到 `config.py` 中：

```python
# MySQL数据库配置
mysql_host: str = "rm-uf6psdjix97ed81r8.mysql.rds.aliyuncs.com"
mysql_port: int = 3306
mysql_user: str = "bot"
mysql_password: str = "yaoboan19990312!"
mysql_database: str = "yba_ppmt"
mysql_charset: str = "utf8mb4"
```

## 数据库表结构

### 商品表 `products` 的结构：

```sql
CREATE TABLE `products` (
    `product_id` int NOT NULL COMMENT '商品ID',
    `original_name` varchar(64) NOT NULL COMMENT '原始名称',
    `nick_name` varchar(64) NULL COMMENT '显示昵称',
    `product_img` varchar(255) NULL COMMENT '商品图片',
    `match_names` json NULL COMMENT '匹配名称列表',
    `series_key` varchar(64) NULL COMMENT '系列名称',
    `series_variant` varchar(64) NULL COMMENT '系列品类',
    `cdn_info` json NULL COMMENT 'CDN信息',
    `cdn_update` datetime NULL COMMENT 'CDN更新时间',
    `created_at` datetime NOT NULL COMMENT '条目创建时间',
    PRIMARY KEY (`product_id`)
) ENGINE=InnoDB DEFAULT CHARACTER SET=utf8mb4;
```

### 店铺表 `stores` 的结构：

```sql
CREATE TABLE `stores` (
    `store_id` int NOT NULL COMMENT '店铺ID',
    `store_name` varchar(32) NOT NULL COMMENT '店铺名',
    `match_names` json DEFAULT NULL COMMENT '匹配名称',
    `group_ids` json DEFAULT NULL COMMENT '群组ID',
    `location` varchar(32) DEFAULT NULL COMMENT '地区',
    `opening` tinyint(1) NOT NULL DEFAULT '1' COMMENT '开业状态',
    `created_at` datetime NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 为group_ids字段创建多值索引（MySQL 8.0.17+）
ALTER TABLE `stores`
  ADD INDEX `idx_group_ids_mvi` (
    (CAST(`group_ids` AS CHAR(128) CHARACTER SET binary ARRAY))
  );
```

## 使用方法

### 1. 基本使用

```python
from utils.database import init_database, close_database
from utils.product_matcher import init_matcher_from_database, match_products
from utils.store_matcher import init_store_matcher_from_database, get_store_by_group_id, match_stores_by_name

async def main():
    # 初始化数据库
    await init_database()

    # 从数据库初始化商品匹配器
    await init_matcher_from_database()

    # 从数据库初始化店铺匹配器
    await init_store_matcher_from_database()

    # 进行商品匹配
    text = "我想要DIMOO心动特调系列的盲盒"
    matches = match_products(text, max_edits=1)

    for match in matches:
        print(f"商品: {match['商品名称']} (ID: {match['商品ID']})")

    # 通过group_id查找店铺（高性能内存索引）
    group_id = "example_group@chatroom"
    store = get_store_by_group_id(group_id)
    if store:
        print(f"群 {group_id} 对应店铺: {store['店铺名称']} (ID: {store['店铺ID']})")

    # 进行店铺名称匹配（精确匹配，不允许错字）
    store_match = match_stores_by_name("泡泡玛特", max_edits=0)
    if store_match:
        print(f"店铺: {store_match['店铺名称']} (ID: {store_match['店铺ID']})")

    # 如果需要允许错字，可以设置max_edits > 0
    store_match_fuzzy = match_stores_by_name("泡泡马特", max_edits=1)  # 允许1个错字
    if store_match_fuzzy:
        print(f"模糊匹配店铺: {store_match_fuzzy['店铺名称']}")

    # 关闭数据库连接
    await close_database()
```

### 2. 数据库操作

```python
from utils.database import get_product_database, get_store_database

async def get_data():
    product_db = get_product_database()
    store_db = get_store_database()

    # 获取所有商品
    all_products = await product_db.get_all_products()

    # 根据ID获取特定商品
    product = await product_db.get_product_by_id(123)

    # 获取所有店铺
    all_stores = await store_db.get_all_stores()

    # 通过group_id查询店铺（使用多值索引）
    store = await store_db.get_store_by_group_id("example_group@chatroom")

    return all_products, product, all_stores, store
```

### 3. 商品匹配

```python
from utils.product_matcher import match_products

# 精确匹配
matches = match_products("DIMOO心动特调系列", max_edits=0)

# 模糊匹配（允许1个错别字）
matches = match_products("DIMO心动特调系列", max_edits=1)

# 系列表达式匹配
matches = match_products("DIMOO心动特调系列-软脸毛绒钥匙扣")
```

## 匹配结果格式

商品匹配返回的结果格式：

```python
[
    {
        "商品ID": 123,
        "商品名称": "DIMOO心动特调系列盲盒",
        "匹配信息": {
            "匹配模式": "dimoo心动特调系列",
            "原始文本中的位置": [5, 15],
            "错字信息列表": []
        }
    }
]
```

## 集成到主程序

主程序 `main.py` 已自动集成数据库、商品匹配和店铺匹配功能：

1. 启动时自动初始化数据库连接
2. 从数据库加载商品数据到匹配器
3. 从数据库加载店铺数据到匹配器，建立group_id内存索引
4. 在消息处理中自动进行商品匹配和店铺查找
5. 停止时自动关闭数据库连接

## 店铺匹配功能特性

### 1. group_id内存索引
- 启动时一次性加载所有店铺数据到内存
- 建立group_id到店铺的快速映射索引
- 查询性能比数据库查询提升数十倍
- 适合频繁的群组到店铺映射查询

### 2. 店铺名称匹配
- 每次只返回一个最精确的匹配结果
- 默认不允许错字（max_edits=0），确保匹配准确性
- 支持别名匹配（通过match_names字段）
- 可选择性允许模糊匹配（设置max_edits > 0）
- 返回匹配置信度和匹配模式

### 3. 数据库多值索引支持
- 使用MySQL 8.0.17+的多值索引特性
- 对JSON数组字段group_ids建立高效索引
- 支持MEMBER OF查询语法
- 显著提升数据库查询性能

## 测试和示例

### 运行测试

```bash
# 测试数据库连接和商品匹配
python test_database_integration.py

# 测试店铺数据库集成和匹配功能
python test_store_integration.py

# 查看使用示例
python example_usage.py
```

### 测试内容

1. **数据库连接测试**：验证数据库连接和商品/店铺数据获取
2. **商品匹配测试**：测试各种商品匹配场景
3. **店铺匹配测试**：测试group_id查找和店铺名称匹配
4. **特定查询测试**：测试根据ID查询商品和店铺
5. **性能测试**：对比内存索引和数据库查询的性能差异
6. **集成测试**：模拟实际使用场景

## 依赖项

新增的依赖项已添加到 `requirements.txt`：

```
aiomysql==0.2.0
```

安装依赖：

```bash
pip install -r requirements.txt
```

## 注意事项

1. **数据库连接**：确保数据库服务器可访问，网络连接稳定
2. **MySQL版本要求**：店铺多值索引功能需要MySQL 8.0.17+
3. **性能优化**：商品和店铺匹配器在启动时一次性加载所有数据，适合数据量不是特别大的场景
4. **内存使用**：店铺匹配器会将所有开业店铺数据加载到内存，注意内存使用情况
5. **店铺状态**：只加载和查询开业状态的店铺（opening=1）
6. **匹配精度**：店铺名称匹配默认不允许错字，确保匹配准确性
7. **错误处理**：所有数据库操作都包含异常处理，确保程序稳定性
8. **资源管理**：使用连接池管理数据库连接，自动处理连接的创建和释放
9. **JSON格式**：确保stores表的group_ids字段存储的是有效的JSON数组格式

## 扩展功能

### 1. 添加新的匹配规则

可以在 `product_matcher.py` 中扩展匹配算法：

```python
def custom_match_rule(text: str, products: List[ProductInfo]) -> List[MatchCandidate]:
    # 自定义匹配逻辑
    pass
```

### 2. 数据库操作扩展

可以在 `database.py` 中添加更多数据库操作：

```python
async def update_product(self, product_id: int, updates: Dict[str, Any]) -> bool:
    # 更新商品信息
    pass

async def search_products(self, keyword: str) -> List[Dict[str, Any]]:
    # 搜索商品
    pass
```

### 3. 缓存优化

可以添加Redis缓存来提高性能：

```python
# 缓存商品数据
# 定期刷新匹配器
# 缓存匹配结果
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查网络连接
   - 验证数据库配置信息
   - 确认数据库服务状态

2. **商品匹配无结果**
   - 检查数据库中是否有商品数据
   - 验证匹配器是否正确初始化
   - 调整匹配参数（如编辑距离）

3. **性能问题**
   - 检查数据库查询性能
   - 考虑添加索引
   - 优化匹配算法

### 日志调试

启用详细日志来调试问题：

```python
import logging
logging.getLogger('aiomysql').setLevel(logging.DEBUG)
```
