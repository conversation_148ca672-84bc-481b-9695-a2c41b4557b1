"""
调试中括号匹配问题
"""

from product_matcher import ProductMatcher, normalize, _fuzzy_keyword_match

def debug_bracket_matching():
    print("=" * 60)
    print("调试中括号匹配问题")
    print("=" * 60)
    
    # 测试商品数据
    test_products = [
        {
            "id": 4880,
            "name": "DIMOO心动特调系列-软脸毛绒钥匙扣盲盒",
            "match_names": ["DIMOO心动特调系列-软脸毛绒钥匙扣盲盒"],
            "series": {"key": "DIMOO心动特调系列", "variant": "钥匙扣"}
        },
        {
            "id": 4881,
            "name": "DIMOO心动特调系列-水晶球",
            "match_names": ["DIMOO心动特调系列-水晶球"],
            "series": {"key": "DIMOO心动特调系列", "variant": "水晶球"}
        },
        {
            "id": 4882,
            "name": "DIMOO心动特调系列-咖啡杯",
            "match_names": ["DIMOO心动特调系列-咖啡杯"],
            "series": {"key": "DIMOO心动特调系列", "variant": "咖啡杯"}
        },
        {
            "id": 4884,
            "name": "DIMOO心动特调系列-盲盒亚克力冰箱贴夹子",
            "match_names": ["DIMOO心动特调系列-盲盒亚克力冰箱贴夹子"],
            "series": {"key": "DIMOO心动特调系列", "variant": "冰箱贴"}
        },
        {
            "id": 4885,
            "name": "DIMOO心动特调系列-盲盒手机挂链",
            "match_names": ["DIMOO心动特调系列-盲盒手机挂链"],
            "series": {"key": "DIMOO心动特调系列", "variant": "手机"}
        },
        {
            "id": 4886,
            "name": "DIMOO心动特调系列-香包挂件套装",
            "match_names": ["DIMOO心动特调系列-香包挂件套装"],
            "series": {"key": "DIMOO心动特调系列", "variant": "香包"}
        }
    ]
    
    matcher = ProductMatcher(test_products)
    
    # 中括号测试文本
    test_text = "DIMOO心动特调系列-衍生品【软脸毛绒钥匙扣盲盒/水晶球/咖啡杯/盲盒亚克力冰箱贴夹子/盲盒手机挂链/香包挂件套装】"
    
    print("1. 系列映射检查")
    print("-" * 40)
    
    series_norm = normalize("DIMOO心动特调系列")
    print(f"系列规范化: '{series_norm}'")
    print(f"系列映射中的键: {list(matcher.series_map.keys())}")
    print(f"是否存在该系列: {series_norm in matcher.series_map}")
    
    if series_norm in matcher.series_map:
        print(f"该系列的变体: {list(matcher.series_map[series_norm].keys())}")
        for variant_key, product in matcher.series_map[series_norm].items():
            print(f"  - 变体键: '{variant_key}' -> 商品: {product.name}")
    
    print()
    
    print("2. 中括号项目解析")
    print("-" * 40)
    
    import re
    bracket_pattern = re.compile(r'([^【]+?)(?:系列)?-?衍生品?【([^】]+)】')
    match = bracket_pattern.search(test_text)
    
    if match:
        series_prefix = match.group(1).strip()
        items_str = match.group(2)
        
        print(f"系列前缀: '{series_prefix}'")
        print(f"项目字符串: '{items_str}'")
        
        # 分割项目
        items = re.split(r'[/／、，,]', items_str)
        print(f"分割后的项目: {items}")
        
        print()
        print("3. 逐项匹配测试")
        print("-" * 40)
        
        for i, item in enumerate(items, 1):
            item = item.strip()
            if not item:
                continue
                
            item_norm = normalize(item)
            print(f"{i}. 项目: '{item}' -> 规范化: '{item_norm}'")
            
            # 检查每个变体
            if series_norm in matcher.series_map:
                for variant_key, product in matcher.series_map[series_norm].items():
                    product_name_norm = normalize(product.name)
                    variant_norm = normalize(variant_key) if variant_key != "main" else ""
                    
                    print(f"   检查商品: {product.name}")
                    print(f"   商品规范化: '{product_name_norm}'")
                    print(f"   变体键规范化: '{variant_norm}'")
                    
                    # 测试各种匹配策略
                    match1 = variant_norm and item_norm in variant_norm
                    match2 = item_norm in product_name_norm
                    match3 = any(keyword in product_name_norm for keyword in item_norm.split() if len(keyword) > 1)
                    match4 = _fuzzy_keyword_match(item_norm, product_name_norm)
                    
                    print(f"   策略1 (变体键包含): {match1}")
                    print(f"   策略2 (商品名包含): {match2}")
                    print(f"   策略3 (关键词匹配): {match3}")
                    print(f"   策略4 (模糊匹配): {match4}")
                    
                    is_match = match1 or match2 or match3 or match4
                    print(f"   最终匹配结果: {'✅' if is_match else '❌'}")
                    print()
    
    print("4. 实际匹配结果")
    print("-" * 40)
    
    results = matcher.match(test_text, max_edits=1)
    print(f"匹配到 {len(results)} 个商品:")
    for result in results:
        print(f"  - {result['商品ID']}: {result['商品名称']}")


if __name__ == "__main__":
    debug_bracket_matching()
