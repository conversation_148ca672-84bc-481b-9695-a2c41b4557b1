"""
测试店铺匹配API变更
验证新的店铺匹配行为：
1. 只返回一个最精确的结果
2. 默认不允许错字
3. 只查询开业的店铺
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from loguru import logger
from utils.database import init_database, close_database
from utils.store_matcher import (
    init_store_matcher_from_database,
    get_store_by_group_id,
    match_stores_by_name,
    get_global_store_matcher
)


async def test_single_result_behavior():
    """测试单一结果返回行为"""
    logger.info("=== 测试单一结果返回行为 ===")
    
    # 测试精确匹配
    logger.info("\n1. 测试精确匹配（应该返回单个结果）")
    result = match_stores_by_name("泡泡玛特", max_edits=0)
    
    if result:
        logger.info(f"✅ 返回类型: {type(result)}")
        logger.info(f"✅ 店铺名称: {result['店铺名称']}")
        logger.info(f"✅ 匹配模式: {result['匹配信息']['匹配模式']}")
        logger.info(f"✅ 置信度: {result['匹配信息']['置信度']}")
    else:
        logger.info("❌ 未找到匹配结果")
    
    # 测试不存在的店铺
    logger.info("\n2. 测试不存在的店铺（应该返回None）")
    result = match_stores_by_name("不存在的店铺", max_edits=0)
    
    if result is None:
        logger.info("✅ 正确返回None")
    else:
        logger.error(f"❌ 应该返回None，但返回了: {result}")


async def test_no_typo_default():
    """测试默认不允许错字的行为"""
    logger.info("\n=== 测试默认不允许错字的行为 ===")
    
    # 测试错字（默认参数，应该找不到）
    logger.info("\n1. 测试错字（默认参数max_edits=0）")
    result = match_stores_by_name("泡泡马特")  # 故意写错字
    
    if result is None:
        logger.info("✅ 默认不允许错字，正确返回None")
    else:
        logger.error(f"❌ 应该返回None，但找到了: {result['店铺名称']}")
    
    # 测试错字（显式允许1个错字）
    logger.info("\n2. 测试错字（显式设置max_edits=1）")
    result = match_stores_by_name("泡泡马特", max_edits=1)
    
    if result:
        logger.info(f"✅ 允许错字后找到: {result['店铺名称']}")
        logger.info(f"✅ 匹配模式: {result['匹配信息']['匹配模式']}")
        logger.info(f"✅ 置信度: {result['匹配信息']['置信度']:.2f}")
    else:
        logger.info("❌ 允许错字后仍未找到")


async def test_opening_stores_only():
    """测试只查询开业店铺的行为"""
    logger.info("\n=== 测试只查询开业店铺的行为 ===")
    
    from utils.database import get_store_database
    
    store_db = get_store_database()
    if not store_db:
        logger.error("❌ 无法获取店铺数据库实例")
        return
    
    # 获取所有店铺（包括未开业的）
    logger.info("\n1. 检查数据库中的店铺状态")
    try:
        # 修改SQL查询以获取所有店铺（包括未开业的）
        conn = await store_db.db_manager.get_connection()
        async with conn.cursor() as cursor:
            await cursor.execute("SELECT store_id, store_name, opening FROM stores ORDER BY store_id")
            all_stores = await cursor.fetchall()
            
            opening_count = sum(1 for store in all_stores if store[2])
            closed_count = len(all_stores) - opening_count
            
            logger.info(f"数据库中总店铺数: {len(all_stores)}")
            logger.info(f"开业店铺数: {opening_count}")
            logger.info(f"未开业店铺数: {closed_count}")
            
        await store_db.db_manager.release_connection(conn)
        
    except Exception as e:
        logger.error(f"查询数据库失败: {e}")
        return
    
    # 检查匹配器只加载开业店铺
    logger.info("\n2. 检查匹配器只加载开业店铺")
    matcher = get_global_store_matcher()
    if matcher:
        loaded_count = len(matcher.stores)
        indexed_groups = len(matcher.group_id_index)
        
        logger.info(f"匹配器加载的店铺数: {loaded_count}")
        logger.info(f"group_id索引数: {indexed_groups}")
        
        if loaded_count == opening_count:
            logger.info("✅ 匹配器正确只加载了开业店铺")
        else:
            logger.error(f"❌ 匹配器加载数量不符，期望: {opening_count}, 实际: {loaded_count}")


async def test_group_id_opening_filter():
    """测试group_id查询的开业店铺过滤"""
    logger.info("\n=== 测试group_id查询的开业店铺过滤 ===")
    
    # 这里需要实际的测试数据
    # 如果你有未开业店铺的group_id，可以测试是否能查到
    test_group_ids = [
        "test_opening_store@chatroom",    # 假设这是开业店铺的群
        "test_closed_store@chatroom",     # 假设这是未开业店铺的群
    ]
    
    for group_id in test_group_ids:
        logger.info(f"\n测试group_id: {group_id}")
        
        # 内存索引查询
        store = get_store_by_group_id(group_id)
        if store:
            opening_status = store.get('开业状态', True)
            logger.info(f"内存索引找到店铺: {store['店铺名称']}, 开业状态: {opening_status}")
            if opening_status:
                logger.info("✅ 正确返回开业店铺")
            else:
                logger.error("❌ 返回了未开业店铺")
        else:
            logger.info("内存索引未找到店铺")
        
        # 数据库查询
        from utils.database import get_store_database
        store_db = get_store_database()
        if store_db:
            try:
                db_store = await store_db.get_store_by_group_id(group_id)
                if db_store:
                    opening_status = db_store.get('opening', True)
                    logger.info(f"数据库查询找到店铺: {db_store['name']}, 开业状态: {opening_status}")
                    if opening_status:
                        logger.info("✅ 数据库查询正确返回开业店铺")
                    else:
                        logger.error("❌ 数据库查询返回了未开业店铺")
                else:
                    logger.info("数据库查询未找到店铺")
            except Exception as e:
                logger.error(f"数据库查询失败: {e}")


async def main():
    """主测试函数"""
    logger.info("开始测试店铺匹配API变更")
    
    try:
        # 初始化数据库和匹配器
        await init_database()
        await init_store_matcher_from_database()
        
        # 运行测试
        await test_single_result_behavior()
        await test_no_typo_default()
        await test_opening_stores_only()
        await test_group_id_opening_filter()
        
        logger.info("\n✅ 所有API变更测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
    
    finally:
        # 关闭数据库连接
        await close_database()
        logger.info("数据库连接已关闭")


if __name__ == "__main__":
    asyncio.run(main())
