"""
商品匹配结果MQ发布器
"""
import json
import aio_pika
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from loguru import logger

from config import settings


class ProductMatchPublisher:
    """商品匹配结果发布器"""
    
    def __init__(self):
        self.connection = None
        self.channel = None
        self.exchange = None
        
    async def connect(self):
        """连接到RabbitMQ"""
        try:
            connection_url = f"amqp://{settings.rabbitmq_username}:{settings.rabbitmq_password}@{settings.rabbitmq_host}:{settings.rabbitmq_port}{settings.rabbitmq_vhost}"
            self.connection = await aio_pika.connect_robust(connection_url)
            self.channel = await self.connection.channel()
            
            # 声明交换机
            self.exchange = await self.channel.declare_exchange(
                "events.product-matches",
                aio_pika.ExchangeType.TOPIC,
                durable=True
            )
            
            logger.info("Product match publisher connected to RabbitMQ")
            
        except Exception as e:
            logger.error(f"Failed to connect product match publisher: {e}")
            raise
    
    async def disconnect(self):
        """断开连接"""
        if self.connection:
            await self.connection.close()
            logger.info("Product match publisher disconnected")
    
    async def publish_product_matches(
        self,
        ts: str,
        msg_id: int,
        store: Dict[str, Any],
        products: List[Dict[str, Any]]
    ) -> bool:
        """
        发布商品匹配结果到MQ
        
        Args:
            ts: 时间戳
            msg_id: 消息ID
            store: 店铺信息 {"id": int, "name": str}
            products: 商品信息列表 [{"id": int, "name": str}, ...]
            
        Returns:
            bool: 是否发布成功
        """
        if not self.exchange:
            logger.error("Publisher not connected")
            return False
            
        if not products:
            logger.debug("No products to publish")
            return True
            
        try:
            # 构建事件载荷
            payload = {
                "ts": ts,
                "msg_id": msg_id,
                "store": store,
                "products": products
            }
            
            # 创建消息
            message = aio_pika.Message(
                body=json.dumps(payload, ensure_ascii=False).encode("utf-8"),
                content_type="application/json",
                delivery_mode=aio_pika.DeliveryMode.PERSISTENT,
                headers={
                    "event": "product.matches.v1",
                    "store_id": str(store["id"])
                }
            )
            
            # 发布消息
            routing_key = f"product.matches.store.{store['id']}"
            await self.exchange.publish(message, routing_key=routing_key)
            
            logger.info(f"Published product matches: store_id={store['id']}, products_count={len(products)}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to publish product matches: {e}")
            return False


# 全局发布器实例
_global_publisher: Optional[ProductMatchPublisher] = None


async def init_product_match_publisher():
    """初始化全局商品匹配发布器"""
    global _global_publisher
    _global_publisher = ProductMatchPublisher()
    await _global_publisher.connect()


async def close_product_match_publisher():
    """关闭全局商品匹配发布器"""
    global _global_publisher
    if _global_publisher:
        await _global_publisher.disconnect()
        _global_publisher = None


def get_product_match_publisher() -> Optional[ProductMatchPublisher]:
    """获取全局商品匹配发布器"""
    return _global_publisher


async def publish_product_matches(
    ts: str,
    msg_id: int,
    store: Dict[str, Any],
    products: List[Dict[str, Any]]
) -> bool:
    """
    发布商品匹配结果（便捷函数）
    """
    publisher = get_product_match_publisher()
    if not publisher:
        logger.error("Product match publisher not initialized")
        return False
    
    return await publisher.publish_product_matches(ts, msg_id, store, products)
