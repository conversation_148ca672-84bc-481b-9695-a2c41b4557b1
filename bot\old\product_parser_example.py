"""
商品解析库使用示例
"""
import logging
from product_parser import ProductParser

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def example_usage():
    """商品解析库使用示例"""
    
    # 1. 创建解析器实例
    parser = ProductParser(logger)
    
    # 2. 准备测试数据
    # 商品数据示例
    product_data = [
        {
            "product_id": "001",
            "product_name": "LABUBU 毛绒公仔",
            "nick_name": "拉布布毛绒"
        },
        {
            "product_id": "002", 
            "product_name": "SKULLPANDA 盲盒系列",
            "nick_name": "骷髅熊猫盲盒"
        },
        {
            "product_id": "003",
            "product_name": "MOLLY 手办套装",
            "nick_name": "茉莉手办"
        },
        {
            "product_id": "004",
            "product_name": "DIMOO 便利店系列",
            "nick_name": "DIMOO便利"
        }
    ]
    
    # 店铺群组关联数据示例
    store_groups_data = [
        {
            "store_id": "store_001",
            "group_id": "12345678@chatroom",
            "store_name": "北京朝阳大悦城店",
            "original_name": "北京朝阳大悦城店"
        },
        {
            "store_id": "store_002", 
            "group_id": "87654321@chatroom",
            "store_name": "上海南京路店",
            "original_name": "上海南京路店"
        }
    ]
    
    # 店铺别名数据示例
    store_aliases_data = [
        {
            "store_id": "store_001",
            "original_name": "北京朝阳大悦城店",
            "alias": "北京朝阳大悦城"
        },
        {
            "store_id": "store_001",
            "original_name": "北京朝阳大悦城店", 
            "alias": "朝阳大悦城"
        },
        {
            "store_id": "store_002",
            "original_name": "上海南京路店",
            "alias": "上海南京路"
        },
        {
            "store_id": "store_002",
            "original_name": "上海南京路店",
            "alias": "南京路"
        }
    ]
    
    # 3. 设置数据
    parser.set_product_data(product_data)
    parser.set_store_data([])  # 店铺数据可以为空，主要使用关联数据
    
    # 4. 测试消息解析
    print("=" * 50)
    print("测试1: 通过群ID匹配店铺")
    print("=" * 50)
    
    test_message_1 = """LABUBU 毛绒公仔
SKULLPANDA 盲盒系列
MOLLY 手办套装"""
    
    result_1 = parser.parse_products_from_message(
        message_text=test_message_1,
        group_id="12345678@chatroom",
        store_groups_data=store_groups_data,
        store_aliases_data=store_aliases_data
    )
    
    print(f"匹配店铺: {result_1['匹配店铺名称']} (ID: {result_1['匹配店铺ID']})")
    print(f"商品匹配结果数量: {len(result_1['商品匹配结果'])}")
    for product in result_1['商品匹配结果']:
        print(f"  - {product['商品名称']} -> {product['匹配商品名称']} (相似度: {product.get('相似度', 1.0):.2f})")
    
    print("\n" + "=" * 50)
    print("测试2: 通过店铺名称匹配")
    print("=" * 50)
    
    test_message_2 = """泡泡玛特 上海南京路店 -------- 监控信息 14:30:25
DIMOO 便利店系列
LABUBU 毛绒公仔
新品上架通知"""
    
    result_2 = parser.parse_products_from_message(
        message_text=test_message_2,
        store_groups_data=store_groups_data,
        store_aliases_data=store_aliases_data
    )
    
    print(f"原始店铺名: {result_2['原始店铺名称']}")
    print(f"匹配店铺: {result_2['匹配店铺名称']} (ID: {result_2['匹配店铺ID']})")
    print(f"商品匹配结果数量: {len(result_2['商品匹配结果'])}")
    for product in result_2['商品匹配结果']:
        print(f"  - {product['商品名称']} -> {product['匹配商品名称']} (相似度: {product.get('相似度', 1.0):.2f})")
    
    print("\n" + "=" * 50)
    print("测试3: 单独测试商品匹配")
    print("=" * 50)
    
    # 测试单个商品匹配
    test_products = ["LABUBU毛绒", "骷髅熊猫", "茉莉手办", "DIMOO便利店"]
    
    for product_name in test_products:
        match_result = parser._match_product(product_name, threshold=0.8, enable_substring_match=True)
        print(f"输入: '{product_name}' -> 匹配状态: {match_result['匹配状态']}")
        if match_result['匹配状态'] == '已匹配':
            print(f"  匹配商品: {match_result['匹配商品名称']} (类型: {match_result['匹配类型']}, 相似度: {match_result.get('相似度', 1.0):.2f})")
    
    print("\n" + "=" * 50)
    print("测试4: 店铺匹配测试")
    print("=" * 50)
    
    test_store_names = ["朝阳大悦城", "南京路", "北京朝阳", "上海南京路店"]
    
    for store_name in test_store_names:
        matched_name, matched_id = parser.find_best_match_store(store_name, store_aliases_data)
        print(f"输入店铺: '{store_name}' -> 匹配结果: {matched_name} (ID: {matched_id})")

def test_complex_message():
    """测试复杂消息解析"""
    parser = ProductParser(logger)
    
    # 设置测试数据
    product_data = [
        {"product_id": "001", "product_name": "LABUBU 怪味便利店系列", "nick_name": "拉布布便利店"},
        {"product_id": "002", "product_name": "SKULLPANDA 城市漫游系列", "nick_name": "骷髅熊猫城市"},
        {"product_id": "003", "product_name": "MOLLY 星座系列盲盒", "nick_name": "茉莉星座"},
        {"product_id": "004", "product_name": "DIMOO 太空旅行系列", "nick_name": "DIMOO太空"},
        {"product_id": "005", "product_name": "PUCKY 森林音乐会", "nick_name": "PUCKY森林"}
    ]
    
    parser.set_product_data(product_data)
    
    print("\n" + "=" * 50)
    print("测试复杂消息解析")
    print("=" * 50)
    
    complex_message = """泡泡玛特北京三里屯店 -------- 补货监控 15:45:30
LABUBU怪味便利店、SKULLPANDA城市漫游
MOLLY星座盲盒+DIMOO太空系列
PUCKY森林音乐会
广告商品不包括
没有补货的商品忽略"""
    
    result = parser.extract_and_match_product_info(complex_message)
    
    print(f"提取店铺名: {result['店铺名']}")
    print(f"提取时间: {result['时间']}")
    print(f"商品列表: {result['商品列表']}")
    print(f"匹配结果数量: {len(result['匹配结果'])}")
    
    for i, match in enumerate(result['匹配结果'], 1):
        print(f"  {i}. 原始: '{match.get('提取商品名称', match['商品名称'])}' -> "
              f"匹配: {match.get('匹配商品名称', '未匹配')} "
              f"(类型: {match.get('匹配类型', 'N/A')}, 相似度: {match.get('相似度', 0):.2f})")

if __name__ == "__main__":
    print("商品解析库使用示例")
    print("=" * 50)
    
    # 运行基本示例
    example_usage()
    
    # 运行复杂消息测试
    test_complex_message()
    
    print("\n示例运行完成！")
