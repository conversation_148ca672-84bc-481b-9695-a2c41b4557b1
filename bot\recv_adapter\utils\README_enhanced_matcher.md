# 增强版商品匹配器使用指南

## 概述

已将泡泡玛特专用解析器的功能完全整合到 `product_matcher_v2.py` 中，现在单个文件就能处理所有复杂的商品表达式，无需额外的专用匹配器。

## 🚀 整合的功能

### 新增的表达式解析能力

1. **中括号衍生品表达**：
   ```
   DIMOO心动特调系列-衍生品【软脸毛绒钥匙扣盲盒/水晶球/咖啡杯/盲盒亚克力冰箱贴夹子/盲盒手机挂链/香包挂件套装】
   ```

2. **系列-子商品表达**：
   ```
   DIMOO心动特调系列-搪胶毛绒吊卡
   CRYBABY悲伤俱乐部系列-硅胶毛绒耳机包
   ```

3. **分号分隔列表**：
   ```
   迪士尼公主创想世界系列手办；KUBO日落之城系列手办；浪漫指尖系列4场景手办；
   ```

### 保留的原有功能

4. **简单变体表达**：
   ```
   ABC-A/B    # 连字符+斜杠
   ABC/A/B    # 纯斜杠
   ```

5. **自然语言表达**：
   ```
   ABC及其衍生品A和B
   ```

6. **精确匹配 + 模糊匹配**：支持错别字纠正
7. **智能去重**：最长匹配优先，避免重叠

## 📋 数据格式支持

### 格式1：通用格式（原有）
```python
[
    {
        "id": 123,
        "name": "ABC",
        "match_names": ["ABC", "A B C"],
        "series": {"key": "ABC", "variant": "A"}
    },
    ...
]
```

### 格式2：泡泡玛特原始格式（新增转换工具）
```python
raw_products = [
    "迪士尼公主创想世界系列手办",
    "DIMOO心动特调系列-搪胶毛绒吊卡，DIMOO心动特调系列",
    "DIMOO心动特调系列-软脸毛绒钥匙扣盲盒，DIMOO心动特调系列，钥匙扣",
    ...
]

# 使用转换工具
from utils.product_matcher_v2 import convert_popmart_data
catalog = convert_popmart_data(raw_products)
```

## 🔧 使用方法

### 基础使用
```python
from utils.product_matcher_v2 import ProductMatcher, convert_popmart_data

# 方法1：直接使用通用格式
matcher = ProductMatcher(your_catalog)
results = matcher.match("文本内容", max_edits=1)

# 方法2：转换泡泡玛特格式
raw_products = ["迪士尼公主创想世界系列手办", "DIMOO心动特调系列-搪胶毛绒吊卡，DIMOO心动特调系列"]
catalog = convert_popmart_data(raw_products)
matcher = ProductMatcher(catalog)
results = matcher.match("文本内容", max_edits=1)
```

### 全局匹配器
```python
from utils.product_matcher_v2 import set_global_matcher, match_products

# 设置一次
set_global_matcher(catalog)

# 之后只传文本
results = match_products("文本内容", max_edits=1)
```

### MessageHandler 集成（已自动更新）
```python
from handlers.message_handler import MessageHandler

handler = MessageHandler()
handler.product_catalog = your_catalog  # 自动使用增强版匹配器

# 处理消息时会自动进行商品匹配
# 结果在转发数据的 Data.ProductMatches 字段
```

## 📊 返回格式

```python
[
    {
        "商品ID": 5,
        "商品名称": "DIMOO心动特调系列-搪胶毛绒吊卡",
        "匹配信息": {
            "匹配模式": "DIMOO心动特调系列衍生品搪胶毛绒吊卡",
            "原始文本中的位置": [15, 45],
            "错字信息列表": [23]  # 错字在原文中的位置
        }
    }
]
```

## 🧪 测试验证

运行测试文件验证所有功能：
```bash
cd bot/recv_adapter/utils
python test_enhanced_matcher.py
```

测试覆盖：
- ✅ 泡泡玛特数据格式转换
- ✅ 中括号衍生品表达式
- ✅ 系列-子商品表达式
- ✅ 分号分隔列表
- ✅ 简单变体表达式
- ✅ 自然语言表达式
- ✅ 模糊匹配和错字纠正
- ✅ 复杂混合文本处理

## 🔄 迁移说明

### 从 popmart_product_matcher.py 迁移
- **无需修改**：MessageHandler 已自动更新
- **数据格式**：使用 `convert_popmart_data()` 转换原始数据
- **功能增强**：所有泡泡玛特解析功能已整合

### 文件变更
- ✅ `product_matcher_v2.py` - 增强版（整合所有功能）
- ❌ `popmart_product_matcher.py` - 不再需要
- ✅ `message_handler.py` - 已更新使用增强版

## 🎯 支持的表达式示例

### 1. 中括号衍生品
**输入文本**：
```
DIMOO心动特调系列-衍生品【软脸毛绒钥匙扣盲盒/水晶球/咖啡杯】
```
**匹配结果**：
- DIMOO心动特调系列-软脸毛绒钥匙扣盲盒
- DIMOO心动特调系列-水晶球  
- DIMOO心动特调系列-咖啡杯

### 2. 分号分隔列表
**输入文本**：
```
迪士尼公主创想世界系列手办；KUBO日落之城系列手办；浪漫指尖系列4场景手办；
```
**匹配结果**：
- 迪士尼公主创想世界系列手办
- KUBO日落之城系列手办
- 浪漫指尖系列4场景手办

### 3. 系列-子商品
**输入文本**：
```
DIMOO心动特调系列-搪胶毛绒吊卡；MOLLY你好,月亮1/8可动人偶
```
**匹配结果**：
- DIMOO心动特调系列-搪胶毛绒吊卡
- MOLLY你好，月亮1/8可动人偶

## 🚀 性能优势

| 特性 | 原版本 | 增强版本 |
|------|--------|----------|
| 表达式类型 | 3种 | 6种 |
| 泡泡玛特支持 | ❌ | ✅ |
| 数据格式 | 单一 | 多格式+转换工具 |
| 文件数量 | 2个 | 1个 |
| 维护复杂度 | 高 | 低 |
| 功能完整性 | 基础 | 完整 |

## 📝 注意事项

1. **向后兼容**：完全兼容原有API和数据格式
2. **性能优化**：预构建索引，避免重复计算
3. **智能去重**：自动处理重叠匹配，优先选择最长最准确的结果
4. **容错匹配**：支持1个错别字的模糊匹配
5. **位置精确**：返回原始文本中的准确字符位置

## 🎯 最佳实践

1. **数据准备**：
   - 通用格式：直接使用
   - 泡泡玛特格式：使用 `convert_popmart_data()` 转换

2. **性能调优**：
   - 合理设置 `max_edits` 参数（建议0-2）
   - 预构建全局匹配器，避免重复初始化

3. **错误处理**：
   - 监控匹配器错误日志
   - 验证数据格式正确性

4. **测试验证**：
   - 使用真实数据测试
   - 验证复杂表达式解析效果

现在你拥有了一个功能完整、性能优化的统一商品匹配器！
