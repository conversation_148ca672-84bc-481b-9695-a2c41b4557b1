"""
数据库和商品匹配功能使用示例
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from loguru import logger
from utils.database import init_database, close_database, get_product_database
from utils.product_matcher import init_matcher_from_database, match_products, get_global_matcher


async def example_basic_usage():
    """基本使用示例"""
    logger.info("=== 基本使用示例 ===")
    
    # 1. 初始化数据库
    await init_database()
    logger.info("数据库已初始化")
    
    # 2. 从数据库初始化商品匹配器
    await init_matcher_from_database()
    logger.info("商品匹配器已初始化")
    
    # 3. 进行商品匹配
    test_text = "我想要DIMOO心动特调系列的盲盒"
    matches = match_products(test_text)
    
    logger.info(f"输入文本: {test_text}")
    logger.info(f"匹配结果: {len(matches)} 个商品")
    
    for match in matches:
        logger.info(f"  - {match['商品名称']} (ID: {match['商品ID']})")


async def example_advanced_matching():
    """高级匹配示例"""
    logger.info("=== 高级匹配示例 ===")
    
    # 测试不同的匹配场景
    test_cases = [
        {
            "text": "SKULLPANDA暗黑童话系列",
            "description": "精确匹配系列名称"
        },
        {
            "text": "泡泡玛特毛绒玩具",
            "description": "品牌+类型匹配"
        },
        {
            "text": "MOLLY茉莉盲盒",
            "description": "角色名称匹配"
        },
        {
            "text": "DIMOO心动特调-软脸毛绒钥匙扣",
            "description": "系列-子商品匹配"
        },
        {
            "text": "想买个DIMO心动特调的东西",  # 故意拼错
            "description": "模糊匹配（允许错别字）"
        }
    ]
    
    for case in test_cases:
        logger.info(f"\n测试: {case['description']}")
        logger.info(f"文本: {case['text']}")
        
        # 尝试不同的编辑距离
        for max_edits in [0, 1, 2]:
            matches = match_products(case['text'], max_edits=max_edits)
            logger.info(f"  编辑距离 {max_edits}: {len(matches)} 个匹配")
            
            for match in matches[:2]:  # 只显示前2个结果
                match_info = match['匹配信息']
                logger.info(f"    - {match['商品名称']} (模式: {match_info['匹配模式']})")


async def example_database_operations():
    """数据库操作示例"""
    logger.info("=== 数据库操作示例 ===")
    
    product_db = get_product_database()
    
    # 1. 获取所有商品
    all_products = await product_db.get_all_products()
    logger.info(f"数据库中共有 {len(all_products)} 个商品")
    
    # 2. 显示商品统计信息
    series_count = {}
    for product in all_products:
        series_info = product.get('series')
        if series_info and series_info.get('key'):
            series_key = series_info['key']
            series_count[series_key] = series_count.get(series_key, 0) + 1
    
    logger.info("系列商品统计:")
    for series, count in sorted(series_count.items(), key=lambda x: x[1], reverse=True)[:5]:
        logger.info(f"  {series}: {count} 个商品")
    
    # 3. 查询特定商品
    if all_products:
        first_product_id = all_products[0]['id']
        product = await product_db.get_product_by_id(first_product_id)
        if product:
            logger.info(f"\n商品详情 (ID: {first_product_id}):")
            logger.info(f"  名称: {product['name']}")
            logger.info(f"  昵称: {product.get('nick_name', 'N/A')}")
            logger.info(f"  匹配名称: {product.get('match_names', [])}")
            logger.info(f"  系列: {product.get('series', 'N/A')}")


async def example_matcher_info():
    """匹配器信息示例"""
    logger.info("=== 匹配器信息示例 ===")
    
    matcher = get_global_matcher()
    if matcher:
        logger.info(f"匹配器已加载 {len(matcher.products)} 个商品")
        logger.info(f"系列映射数量: {len(matcher.series_map)}")
        
        # 显示一些系列信息
        logger.info("可用系列:")
        for series_key in list(matcher.series_map.keys())[:5]:
            variants = list(matcher.series_map[series_key].keys())
            logger.info(f"  {series_key}: {len(variants)} 个变体")
    else:
        logger.warning("匹配器未初始化")


async def example_text_processing_simulation():
    """模拟文本处理示例"""
    logger.info("=== 文本处理模拟示例 ===")
    
    # 模拟从微信消息中提取的文本
    sample_messages = [
        "群友们，有人要DIMOO心动特调系列的盲盒吗？",
        "刚买了SKULLPANDA暗黑童话系列，太可爱了！",
        "求购MOLLY茉莉的周边产品",
        "泡泡玛特新出的毛绒玩具怎么样？",
        "有没有人要转让DIMOO心动特调-软脸毛绒钥匙扣的？"
    ]
    
    for i, message in enumerate(sample_messages, 1):
        logger.info(f"\n消息 {i}: {message}")
        
        # 进行商品匹配
        matches = match_products(message, max_edits=1)
        
        if matches:
            logger.info(f"识别到 {len(matches)} 个商品:")
            for match in matches:
                product_name = match['商品名称']
                match_info = match['匹配信息']
                position = match_info['原始文本中的位置']
                
                # 高亮显示匹配的部分
                start, end = position
                highlighted = (
                    message[:start] + 
                    f"【{message[start:end]}】" + 
                    message[end:]
                )
                logger.info(f"  - {product_name}")
                logger.info(f"    匹配位置: {highlighted}")
        else:
            logger.info("  未识别到商品")


async def main():
    """主函数"""
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <5}</level> | <cyan>{function}</cyan> - <level>{message}</level>"
    )
    
    logger.info("🚀 数据库和商品匹配功能使用示例")
    
    try:
        # 运行各种示例
        await example_basic_usage()
        await example_advanced_matching()
        await example_database_operations()
        await example_matcher_info()
        await example_text_processing_simulation()
        
        logger.info("🎉 所有示例运行完成")
        
    except Exception as e:
        logger.error(f"❌ 示例运行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        
    finally:
        # 清理资源
        await close_database()
        logger.info("🔒 资源已清理")


if __name__ == "__main__":
    asyncio.run(main())
