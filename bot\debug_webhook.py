#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信Bot Webhook调试程序
用于接收和调试webhook数据
"""

import argparse
import json
import logging
from datetime import datetime
from flask import Flask, request, jsonify

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('webhook_debug.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 全局变量存储目标wxid
TARGET_WXID = None


@app.route('/recv/<wxid>', methods=['POST'])
def receive_webhook(wxid):
    """接收webhook数据"""
    try:

        try:
            msg = request.json
            # print(msg)
        except:
            return jsonify({'status': 'error', 'message': '请求数据格式错误'}), 500

        
        # 检查是否需要过滤wxid
        if TARGET_WXID and wxid != TARGET_WXID:
            logger.debug(f"忽略非目标wxid的消息: {wxid} (目标: {TARGET_WXID})")
            return jsonify({'status': 'ignored', 'reason': 'wxid_filtered'}), 200



        # 只显示push_content内容
        push_content = msg.get('push_content', '')
        if push_content:
            print(f"[V{msg.get('ver',0)}:{wxid}] {push_content}")
        else:
            # 如果没有push_content，显示普通content
            content = msg.get('content', '')
            if content:
                print(f"[V{msg.get('ver',0)}:{wxid}] {content}")
            else:
                print(f"[V{msg.get('ver',0)}:{wxid}] (无内容)")
        
        return jsonify({'status': 'success', 'message': 'ok'}), 200
        
    except Exception as e:
        error_msg = f"处理webhook数据失败: {e}"
        logger.error(error_msg)
        print(f"\n错误: {error_msg}")
        return jsonify({'status': 'error', 'message': str(e)}), 500


@app.route('/status', methods=['GET'])
def status():
    """状态检查接口"""
    return jsonify({
        'status': 'running',
        'target_wxid': TARGET_WXID,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    })


@app.route('/', methods=['GET'])
def index():
    """根路径，显示调试信息"""
    return f"""
    <h1>微信Bot Webhook调试程序</h1>
    <p>状态: 运行中</p>
    <p>目标WXID: {TARGET_WXID or '全部'}</p>
    <p>当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    <h2>接口说明:</h2>
    <ul>
        <li>POST /recv/&lt;wxid&gt; - 接收webhook数据</li>
        <li>GET /status - 查看状态</li>
    </ul>
    """


def main():
    """主函数"""
    global TARGET_WXID
    
    parser = argparse.ArgumentParser(description='微信Bot Webhook调试程序')
    parser.add_argument('--wxid', type=str, help='指定要监听的wxid，不指定则监听所有')
    parser.add_argument('--host', type=str, default='0.0.0.0', help='监听地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=9312, help='监听端口 (默认: 8080)')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    
    args = parser.parse_args()
    
    TARGET_WXID = args.wxid
    
    print("="*60)
    print("微信Bot Webhook调试程序启动")
    print("="*60)
    print(f"监听地址: {args.host}:{args.port}")
    print(f"目标WXID: {TARGET_WXID or '全部'}")
    print(f"调试模式: {'开启' if args.debug else '关闭'}")
    print(f"日志文件: webhook_debug.log")
    print("="*60)
    print("等待webhook数据...")
    print("按 Ctrl+C 停止程序")
    print("="*60)
    
    try:
        app.run(host=args.host, port=args.port, debug=args.debug)
    except KeyboardInterrupt:
        print("\n程序已停止")
    except Exception as e:
        logger.error(f"程序运行错误: {e}")
        print(f"错误: {e}")


if __name__ == '__main__':
    main()
