CREATE TABLE `products` (
	`product_id` int NOT NULL COMMENT '商品ID',
	`original_name` varchar(64) NOT NULL COMMENT '原始名称',
	`nick_name` varchar(64) NULL COMMENT '显示昵称',
	`product_img` varchar(255) NULL COMMENT '商品图片',
	`match_names` json NULL COMMENT '匹配名称列表',
	`series_key` varchar(64) NULL COMMENT '系列名称',
	`series_variant` varchar(64) NULL COMMENT '系列品类',
	`cdn_info` json NULL COMMENT 'CDN信息',
	`created_at` datetime NOT NULL COMMENT '条目创建时间',
	PRIMARY KEY (`product_id`)
) ENGINE=InnoDB
DEFAULT CHARACTER SET=utf8mb4;
