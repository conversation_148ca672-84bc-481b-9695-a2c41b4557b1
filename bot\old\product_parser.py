"""
商品解析库 - 从旧版消息处理文件中提取的商品识别和匹配功能
"""
import re
import difflib
import time
import logging
from typing import Dict, List, Any, Optional, Tuple

# 商品关键词和系列关键词（从旧版代码中提取）
PRODUCT_KEYWORDS = ["公仔", "套装", "手办", "盲盒", "毛绒", "挂件", "吊卡", "搪胶", "系列", "耳机", "手机"]
SERIES_KEYWORDS = ["labubu", "skullpanda", "molly", "dimoo", "pucky", "monsters", "mokoko", "crybaby", 
                   "hirono", "hacipupu", "zsiga", "tinytiny", "kubo", "jelly", "bunny", "nyota", "yuki"]


class ProductParser:
    """商品解析器 - 负责商品识别和匹配"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化商品解析器
        
        Args:
            logger: 日志记录器，如果为None则创建默认记录器
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 商品数据和索引
        self.product_info: List[Dict[str, Any]] = []
        self.product_index: Dict[str, Any] = {"id_map": {}, "normalized_names": {}}
        
        # 店铺数据
        self.store_info: List[Dict[str, Any]] = []
    
    def set_product_data(self, product_list: List[Dict[str, Any]]) -> None:
        """
        设置商品数据并构建索引
        
        Args:
            product_list: 商品列表，每个商品应包含 product_id, product_name 等字段
        """
        self.product_info = product_list
        self.product_index = self._build_product_index(product_list)
        self.logger.info(f"已加载 {len(product_list)} 个商品数据")
    
    def set_store_data(self, store_list: List[Dict[str, Any]]) -> None:
        """
        设置店铺数据
        
        Args:
            store_list: 店铺列表，每个店铺应包含 store_id, original_name 等字段
        """
        self.store_info = store_list
        self.logger.info(f"已加载 {len(store_list)} 个店铺数据")
    
    def _normalize_product_name(self, product_name: str) -> str:
        """
        标准化商品名称
        
        Args:
            product_name: 原始商品名称
            
        Returns:
            标准化后的商品名称
        """
        if not product_name:
            return ""
        
        # 移除空格、特殊字符和常见词
        normalized = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', product_name).strip().lower()
        
        if "怪味" in normalized:
            normalized = normalized.replace("手办", "")
            normalized = normalized.replace("便利店", "便利")
        
        normalized = normalized.replace("衍生品", "")
        return normalized
    
    def _normalize_store_name(self, store_name: str) -> str:
        """
        标准化店铺名称
        
        Args:
            store_name: 原始店铺名称
            
        Returns:
            标准化后的店铺名称
        """
        if not store_name:
            return ""
        
        # 移除空格、特殊字符和常见词
        normalized = store_name.replace(" ", "").replace("（新店）", "").replace("(新店)", "").replace("店", "").replace("泡泡玛特", "").replace("-", "").lower()
        # 移除末尾的"群"和数字
        normalized = re.sub(r'群\d*$|群$', '', normalized)
        return normalized
    
    def _build_product_index(self, product_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        构建商品索引，提高匹配效率
        
        Args:
            product_list: 商品列表
            
        Returns:
            包含多种索引的字典
        """
        if not product_list:
            return {"id_map": {}, "normalized_names": {}}
        
        index = {
            "id_map": {},      # 商品ID到商品对象的映射
            "normalized_names": {}  # 标准化的商品名称到商品ID的映射
        }
        
        for product in product_list:
            product_id = product.get("product_id")
            if not product_id:
                continue
                
            # 创建ID索引
            index["id_map"][product_id] = product
            
            # 创建名称索引
            product_name = product.get("product_name", "")
            if product_name:
                # 创建标准化名称索引
                normalized_name = self._normalize_product_name(product_name)
                index["normalized_names"][normalized_name] = product_id
                
            # 如果有昵称，也加入索引
            nick_name = product.get("nick_name")
            if nick_name:
                normalized_nick = self._normalize_product_name(nick_name)
                index["normalized_names"][normalized_nick] = product_id
        
        return index
    
    def _match_product(self, product_name: str, threshold: float = 0.9, 
                      enable_substring_match: bool = False) -> Dict[str, Any]:
        """
        在商品数据中查找匹配的商品
        
        Args:
            product_name: 商品名称
            threshold: 相似度阈值，默认为0.9
            enable_substring_match: 是否启用子串匹配，默认为False
        
        Returns:
            匹配结果字典
        """
        # 初始化匹配结果
        match_result = {
            "商品名称": product_name,
            "匹配状态": "未匹配",
            "匹配位置": None
        }
        
        try:
            index = self.product_index
            
            if not index or not index["id_map"]:
                return match_result
            
            normalized_name = self._normalize_product_name(product_name)
            matched_info = None
            match_type = None
            similarity = 1.0
            match_position = None
            
            # 1. 精确匹配 - 使用索引
            if normalized_name in index["normalized_names"]:
                product_id = index["normalized_names"][normalized_name]
                matched_info = index["id_map"][product_id]
                match_type = "exact"
            
            # 2. 子串匹配
            elif enable_substring_match:
                longest_match_length = 0
                best_match_id = None
                best_match_position = (0, 0)
                
                # 使用索引中的标准化名称进行匹配
                for db_name, pid in index["normalized_names"].items():
                    if db_name in normalized_name:
                        # 数据库中的名称是输入名称的子串
                        match_len = len(db_name)
                        if match_len > longest_match_length:
                            longest_match_length = match_len
                            best_match_id = pid
                            start_pos = normalized_name.find(db_name)
                            best_match_position = (start_pos, start_pos + match_len)
                
                if best_match_id:
                    matched_info = index["id_map"][best_match_id]
                    match_type = "substring"
                    match_position = best_match_position
            
            # 3. 相似度匹配
            if not matched_info:
                highest_ratio = 0
                best_match_id = None
                
                # 使用索引中的标准化名称进行相似度匹配
                for db_name, pid in index["normalized_names"].items():
                    ratio = difflib.SequenceMatcher(None, normalized_name, db_name).ratio()
                    if ratio > highest_ratio:
                        highest_ratio = ratio
                        best_match_id = pid
                
                # 应用阈值
                if highest_ratio > threshold and best_match_id:
                    matched_info = index["id_map"][best_match_id]
                    match_type = "similarity"
                    similarity = highest_ratio
            
            # 返回结果
            if matched_info:
                match_result.update({
                    "匹配状态": "已匹配",
                    "匹配商品ID": matched_info['product_id'],
                    "匹配商品名称": matched_info['product_name'],
                    "匹配商品对象": matched_info,
                    "相似度": similarity,
                    "匹配类型": match_type,
                    "匹配位置": match_position
                })
            
            return match_result
            
        except Exception as e:
            self.logger.error(f"匹配商品失败: {str(e)}")
            return match_result
    
    def _recursive_match_products(self, text: str, result: Dict[str, List]) -> None:
        """
        递归匹配商品名称，同时处理子串匹配和相似度匹配
        
        Args:
            text: 要匹配的文本
            result: 存储匹配结果的字典
        """
        # 如果文本为空，直接返回
        if not text.strip():
            return
        
        # 排除明确的非商品信息
        if "广告" in text or "没有补" in text or "没补" in text or "监控联系" in text:
            return
        
        # 在递归匹配前先去除空格，保持原始文本用于后续处理
        original_text = text
        text_no_space = self._normalize_product_name(text)
            
        # 尝试匹配商品
        match_result = self._match_product(
            text_no_space,
            threshold=0.9,
            enable_substring_match=True
        )
        
        # 如果匹配成功
        if match_result["匹配状态"] == "已匹配":
            # 添加匹配结果，保留原始文本作为显示
            match_result["提取商品名称"] = original_text
            result["匹配结果"].append(match_result)
            result["商品列表"].append(match_result["匹配商品名称"])
            
            # 如果是子串匹配，处理剩余文本
            if match_result["匹配类型"] == "substring" and match_result.get("匹配位置"):
                start, end = match_result["匹配位置"]
                # 提取剩余文本并递归匹配
                remaining_text = text_no_space[:start].strip() + " " + text_no_space[end:].strip()
                remaining_text = remaining_text.strip()
                
                if len(remaining_text) > 2:
                    # 检查剩余文本是否包含商品关键词
                    if (any(keyword in remaining_text for keyword in PRODUCT_KEYWORDS) or 
                        any(series in remaining_text for series in SERIES_KEYWORDS)):
                        self._recursive_match_products(remaining_text, result)
            
            # 如果是相似度匹配且相似度不是100%，可能还有其他商品
            elif match_result["匹配类型"] == "similarity" and match_result.get("相似度", 1.0) < 1.0:
                # 从原文本中移除已匹配的部分
                matched_name = self._normalize_product_name(match_result["匹配商品名称"])
                remaining_text = text_no_space
                
                # 尝试找到匹配商品名称的位置
                match_pos = remaining_text.find(matched_name)
                if match_pos != -1:
                    # 提取剩余文本
                    remaining_text = remaining_text[:match_pos].strip() + " " + remaining_text[match_pos + len(matched_name):].strip()
                    remaining_text = remaining_text.strip()
                    
                    if len(remaining_text) > 2:
                        # 检查剩余文本是否包含商品关键词
                        if (any(keyword in remaining_text for keyword in PRODUCT_KEYWORDS) or 
                            any(series in remaining_text for series in SERIES_KEYWORDS)):
                            self._recursive_match_products(remaining_text, result)
        else:
            # 如果没有找到匹配，检查是否包含分隔符
            separators = ["、", "，", ",", "+", ";", "；"]
            for separator in separators:
                if separator in original_text:
                    # 按分隔符拆分文本
                    parts = [p.strip() for p in original_text.split(separator) if p.strip()]
                    # 对每个部分进行递归匹配
                    for part in parts:
                        if len(part) > 2:
                            # 检查部分文本是否包含商品关键词
                            if (any(keyword in part.lower() for keyword in PRODUCT_KEYWORDS) or 
                                any(series in part.lower() for series in SERIES_KEYWORDS)):
                                self._recursive_match_products(part, result)
                    return
            
            # 如果没有分隔符且文本不为空，检查是否包含商品关键词
            if len(original_text.strip()) > 2:
                if (any(keyword in original_text.lower() for keyword in PRODUCT_KEYWORDS) or 
                    any(series in original_text.lower() for series in SERIES_KEYWORDS)):
                    result["商品列表"].append(original_text)
                    match_result["商品名称"] = original_text
                    result["匹配结果"].append(match_result)

    def extract_and_match_product_info(self, message_text: str, store_name: Optional[str] = None) -> Dict[str, Any]:
        """
        从消息中提取店铺名、时间和商品信息，并查找匹配的商品

        Args:
            message_text: 接收到的消息文本
            store_name: 店铺名称，如果已知则直接使用

        Returns:
            包含提取和匹配结果的字典
        """
        # 初始化结果字典
        result = {
            "店铺名": None,
            "时间": None,
            "商品列表": [],
            "匹配结果": []
        }

        try:
            if store_name is None:
                # 提取店铺名和时间
                # 假设店铺名在消息的开头，后面跟着多个破折号，然后是监控信息和时间
                store_time_pattern = r'^(.*?)[-]+.*?(\d{1,2}:\d{1,2}:\d{1,2})'
                store_time_match = re.search(store_time_pattern, message_text)
                if store_time_match:
                    # 提取店铺名，去除可能的空格
                    result["店铺名"] = store_time_match.group(1).strip()

                    # 提取时间
                    result["时间"] = store_time_match.group(2)

                    # 删除已提取的内容
                    message_text = re.sub(store_time_pattern, '', message_text, 1).strip()
                else:
                    # 使用正则表达式提取店铺名
                    store_pattern = r'^(?:POP MART|泡泡玛特)\s*(.*?)\s'
                    store_match = re.search(store_pattern, message_text, re.MULTILINE)
                    if store_match:
                        result["店铺名"] = store_match.group(1).strip()
                        # 删除已提取的店铺名部分
                        message_text = re.sub(store_pattern, '', message_text, 1).strip()
                    else:
                        # 尝试以第一行匹配店铺名
                        result["店铺名"] = message_text.split('\n')[0].strip()

                    # 尝试提取时间（如果之前没有提取到）
                    time_pattern = r'(\d{1,2}:\d{1,2}(?::\d{1,2})?)'
                    time_match = re.search(time_pattern, message_text)
                    if time_match:
                        result["时间"] = time_match.group(1)
                        # 删除已提取的时间
                        message_text = re.sub(time_pattern, '', message_text, 1).strip()
            else:
                result["店铺名"] = store_name
                result["时间"] = time.strftime("%H:%M:%S", time.localtime())

            # 提取商品名称
            # 按行分割消息
            lines = message_text.split('\n')

            # 提取所有可能的商品行
            for line in lines:
                line = line.strip()
                # 跳过空行或只包含表情符号的行
                if not line or line.startswith('POP MART'):
                    continue

                # 如果商品名称非空，添加到结果中并进行递归匹配
                product_name = self._normalize_product_name(line)
                if len(product_name) > 2:
                    self._recursive_match_products(product_name, result)

        except Exception as e:
            self.logger.error(f"提取和匹配商品信息失败: {str(e)}")
            result["错误"] = str(e)

        return result

    def find_store_by_group_id(self, group_id: str, store_groups_data: List[Dict[str, Any]]) -> Tuple[Optional[str], Optional[str]]:
        """
        通过群ID查找店铺信息

        Args:
            group_id: 群ID
            store_groups_data: 店铺群组关联数据，格式为 [{"store_id": "xxx", "group_id": "xxx", "store_name": "xxx"}, ...]

        Returns:
            (store_name, store_id) 如果找到匹配的店铺，否则返回 (None, None)
        """
        if not group_id:
            return None, None

        try:
            # 在店铺群组数据中查找匹配的群ID
            for store_group in store_groups_data:
                if store_group.get("group_id") == group_id:
                    store_name = store_group.get("store_name") or store_group.get("original_name")
                    store_id = store_group.get("store_id")
                    if store_name and store_id:
                        return store_name, store_id

        except Exception as e:
            self.logger.error(f"通过群ID查找店铺失败: {str(e)}")

        return None, None

    def find_best_match_store(self, store_name: str, store_aliases_data: List[Dict[str, Any]]) -> Tuple[Optional[str], Optional[str]]:
        """
        查找最匹配的店铺名及其store_id

        Args:
            store_name: 店铺名称
            store_aliases_data: 店铺别名数据，格式为 [{"store_id": "xxx", "original_name": "xxx", "alias": "xxx"}, ...]

        Returns:
            (store_name, store_id) 如果找到匹配的店铺，否则返回 (None, None)
        """
        if not store_name:
            return None, None

        try:
            # 标准化输入的店铺名称
            normalized_store_name = self._normalize_store_name(store_name)

            # 计算相似度
            best_match = None
            highest_ratio = 0

            for store_alias in store_aliases_data:
                store_id = store_alias.get("store_id")
                original_name = store_alias.get("original_name")
                alias = store_alias.get("alias")

                if not all([store_id, original_name, alias]):
                    continue

                # 使用标准化后的名称进行匹配
                ratio = difflib.SequenceMatcher(None, normalized_store_name, alias).ratio()
                if ratio > highest_ratio:
                    highest_ratio = ratio
                    best_match = (original_name, store_id)

            # 如果相似度大于0.66，认为是有效匹配
            if highest_ratio >= 0.66:
                return best_match
            else:
                if best_match:
                    # 记录未匹配的店铺信息到日志
                    self.logger.warning(f"店铺匹配失败: 查询店铺={store_name}(标准化后={normalized_store_name}), "
                                      f"最相似店铺={best_match[0]}, 相似度={highest_ratio}")
                return None, None

        except Exception as e:
            self.logger.error(f"查找店铺失败: {str(e)}")
            return None, None

    def parse_products_from_message(self, message_text: str, group_id: Optional[str] = None,
                                  store_groups_data: Optional[List[Dict[str, Any]]] = None,
                                  store_aliases_data: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        从消息中解析商品和店铺信息的主要方法

        Args:
            message_text: 消息文本
            group_id: 群ID，用于查找店铺信息
            store_groups_data: 店铺群组关联数据
            store_aliases_data: 店铺别名数据

        Returns:
            解析结果字典，包含店铺信息和商品匹配结果
        """
        response_data = {
            "匹配店铺名称": "",
            "匹配店铺ID": "",
            "商品匹配结果": [],
            "原始店铺名称": "",
            "店铺匹配信息": {}
        }

        try:
            matched_store_name = None
            store_id = None
            matched_result = None

            # 如果提供了群ID，尝试通过群ID查找店铺
            if group_id and store_groups_data:
                matched_store_name, store_id = self.find_store_by_group_id(group_id, store_groups_data)
                if matched_store_name and store_id:
                    self.logger.info(f"通过群ID {group_id} 找到匹配的店铺: {matched_store_name}({store_id})")
                    matched_result = self.extract_and_match_product_info(message_text, matched_store_name)

            # 如果没有通过群ID找到店铺，尝试从消息中提取店铺和商品信息
            if not matched_store_name:
                matched_result = self.extract_and_match_product_info(message_text)

                if matched_result.get("店铺名") and matched_result.get("商品列表") and store_aliases_data:
                    # 查找匹配的店铺
                    matched_store_name, store_id = self.find_best_match_store(matched_result["店铺名"], store_aliases_data)

                    if matched_store_name and store_id:
                        self.logger.info(f"通过店铺名匹配找到店铺: {matched_store_name}({store_id})")

            # 构建返回结果
            if matched_store_name and store_id and matched_result:
                response_data.update({
                    "匹配店铺名称": matched_store_name,
                    "匹配店铺ID": store_id,
                    "商品匹配结果": matched_result.get("匹配结果", []),
                    "原始店铺名称": matched_result.get("店铺名", ""),
                    "店铺匹配信息": {
                        "匹配方式": "群ID匹配" if group_id else "名称匹配",
                        "原始名称": matched_result.get("店铺名", ""),
                        "匹配名称": matched_store_name,
                        "店铺ID": store_id,
                        "提取时间": matched_result.get("时间", "")
                    }
                })

                self.logger.info(f"成功解析商品信息: 店铺={matched_store_name}, 商品数量={len(matched_result.get('匹配结果', []))}")
            else:
                self.logger.warning("未找到匹配的店铺或商品")

        except Exception as e:
            self.logger.error(f"解析商品信息失败: {str(e)}")
            response_data["错误"] = str(e)

        return response_data

    def extract_and_match_product_info(self, message_text: str, store_name: Optional[str] = None) -> Dict[str, Any]:
        """
        从消息中提取店铺名、时间和商品信息，并查找匹配的商品

        Args:
            message_text: 接收到的消息文本
            store_name: 店铺名称，如果已知则直接使用

        Returns:
            包含提取和匹配结果的字典
        """
        # 初始化结果字典
        result = {
            "店铺名": None,
            "时间": None,
            "商品列表": [],
            "匹配结果": []
        }

        try:
            if store_name is None:
                # 提取店铺名和时间
                # 假设店铺名在消息的开头，后面跟着多个破折号，然后是监控信息和时间
                store_time_pattern = r'^(.*?)[-]+.*?(\d{1,2}:\d{1,2}:\d{1,2})'
                store_time_match = re.search(store_time_pattern, message_text)
                if store_time_match:
                    # 提取店铺名，去除可能的空格
                    result["店铺名"] = store_time_match.group(1).strip()

                    # 提取时间
                    result["时间"] = store_time_match.group(2)

                    # 删除已提取的内容
                    message_text = re.sub(store_time_pattern, '', message_text, 1).strip()
                else:
                    # 使用正则表达式提取店铺名
                    store_pattern = r'^(?:POP MART|泡泡玛特)\s*(.*?)\s'
                    store_match = re.search(store_pattern, message_text, re.MULTILINE)
                    if store_match:
                        result["店铺名"] = store_match.group(1).strip()
                        # 删除已提取的店铺名部分
                        message_text = re.sub(store_pattern, '', message_text, 1).strip()
                    else:
                        # 尝试以第一行匹配店铺名
                        result["店铺名"] = message_text.split('\n')[0].strip()

                    # 尝试提取时间（如果之前没有提取到）
                    time_pattern = r'(\d{1,2}:\d{1,2}(?::\d{1,2})?)'
                    time_match = re.search(time_pattern, message_text)
                    if time_match:
                        result["时间"] = time_match.group(1)
                        # 删除已提取的时间
                        message_text = re.sub(time_pattern, '', message_text, 1).strip()
            else:
                result["店铺名"] = store_name
                result["时间"] = time.strftime("%H:%M:%S", time.localtime())

            # 提取商品名称
            # 按行分割消息
            lines = message_text.split('\n')

            # 提取所有可能的商品行
            for line in lines:
                line = line.strip()
                # 跳过空行或只包含表情符号的行
                if not line or line.startswith('POP MART'):
                    continue

                # 如果商品名称非空，添加到结果中并进行递归匹配
                product_name = self._normalize_product_name(line)
                if len(product_name) > 2:
                    self._recursive_match_products(product_name, result)

        except Exception as e:
            self.logger.error(f"提取和匹配商品信息失败: {str(e)}")
            result["错误"] = str(e)

        return result

    def find_store_by_group_id(self, group_id: str, store_groups_data: List[Dict[str, Any]]) -> Tuple[Optional[str], Optional[str]]:
        """
        通过群ID查找店铺信息

        Args:
            group_id: 群ID
            store_groups_data: 店铺群组关联数据，格式为 [{"store_id": "xxx", "group_id": "xxx", "store_name": "xxx"}, ...]

        Returns:
            (store_name, store_id) 如果找到匹配的店铺，否则返回 (None, None)
        """
        if not group_id:
            return None, None

        try:
            # 在店铺群组数据中查找匹配的群ID
            for store_group in store_groups_data:
                if store_group.get("group_id") == group_id:
                    store_name = store_group.get("store_name") or store_group.get("original_name")
                    store_id = store_group.get("store_id")
                    if store_name and store_id:
                        return store_name, store_id

        except Exception as e:
            self.logger.error(f"通过群ID查找店铺失败: {str(e)}")

        return None, None

    def find_best_match_store(self, store_name: str, store_aliases_data: List[Dict[str, Any]]) -> Tuple[Optional[str], Optional[str]]:
        """
        查找最匹配的店铺名及其store_id

        Args:
            store_name: 店铺名称
            store_aliases_data: 店铺别名数据，格式为 [{"store_id": "xxx", "original_name": "xxx", "alias": "xxx"}, ...]

        Returns:
            (store_name, store_id) 如果找到匹配的店铺，否则返回 (None, None)
        """
        if not store_name:
            return None, None

        try:
            # 标准化输入的店铺名称
            normalized_store_name = self._normalize_store_name(store_name)

            # 计算相似度
            best_match = None
            highest_ratio = 0

            for store_alias in store_aliases_data:
                store_id = store_alias.get("store_id")
                original_name = store_alias.get("original_name")
                alias = store_alias.get("alias")

                if not all([store_id, original_name, alias]):
                    continue

                # 使用标准化后的名称进行匹配
                ratio = difflib.SequenceMatcher(None, normalized_store_name, alias).ratio()
                if ratio > highest_ratio:
                    highest_ratio = ratio
                    best_match = (original_name, store_id)

            # 如果相似度大于0.66，认为是有效匹配
            if highest_ratio >= 0.66:
                return best_match
            else:
                if best_match:
                    # 记录未匹配的店铺信息到日志
                    self.logger.warning(f"店铺匹配失败: 查询店铺={store_name}(标准化后={normalized_store_name}), "
                                      f"最相似店铺={best_match[0]}, 相似度={highest_ratio}")
                return None, None

        except Exception as e:
            self.logger.error(f"查找店铺失败: {str(e)}")
            return None, None

    def parse_products_from_message(self, message_text: str, group_id: Optional[str] = None,
                                  store_groups_data: Optional[List[Dict[str, Any]]] = None,
                                  store_aliases_data: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        从消息中解析商品和店铺信息的主要方法

        Args:
            message_text: 消息文本
            group_id: 群ID，用于查找店铺信息
            store_groups_data: 店铺群组关联数据
            store_aliases_data: 店铺别名数据

        Returns:
            解析结果字典，包含店铺信息和商品匹配结果
        """
        response_data = {
            "匹配店铺名称": "",
            "匹配店铺ID": "",
            "商品匹配结果": [],
            "原始店铺名称": "",
            "店铺匹配信息": {}
        }

        try:
            matched_store_name = None
            store_id = None
            matched_result = None

            # 如果提供了群ID，尝试通过群ID查找店铺
            if group_id and store_groups_data:
                matched_store_name, store_id = self.find_store_by_group_id(group_id, store_groups_data)
                if matched_store_name and store_id:
                    self.logger.info(f"通过群ID {group_id} 找到匹配的店铺: {matched_store_name}({store_id})")
                    matched_result = self.extract_and_match_product_info(message_text, matched_store_name)

            # 如果没有通过群ID找到店铺，尝试从消息中提取店铺和商品信息
            if not matched_store_name:
                matched_result = self.extract_and_match_product_info(message_text)

                if matched_result.get("店铺名") and matched_result.get("商品列表") and store_aliases_data:
                    # 查找匹配的店铺
                    matched_store_name, store_id = self.find_best_match_store(matched_result["店铺名"], store_aliases_data)

                    if matched_store_name and store_id:
                        self.logger.info(f"通过店铺名匹配找到店铺: {matched_store_name}({store_id})")

            # 构建返回结果
            if matched_store_name and store_id and matched_result:
                response_data.update({
                    "匹配店铺名称": matched_store_name,
                    "匹配店铺ID": store_id,
                    "商品匹配结果": matched_result.get("匹配结果", []),
                    "原始店铺名称": matched_result.get("店铺名", ""),
                    "店铺匹配信息": {
                        "匹配方式": "群ID匹配" if group_id else "名称匹配",
                        "原始名称": matched_result.get("店铺名", ""),
                        "匹配名称": matched_store_name,
                        "店铺ID": store_id,
                        "提取时间": matched_result.get("时间", "")
                    }
                })

                self.logger.info(f"成功解析商品信息: 店铺={matched_store_name}, 商品数量={len(matched_result.get('匹配结果', []))}")
            else:
                self.logger.warning("未找到匹配的店铺或商品")

        except Exception as e:
            self.logger.error(f"解析商品信息失败: {str(e)}")
            response_data["错误"] = str(e)

        return response_data
