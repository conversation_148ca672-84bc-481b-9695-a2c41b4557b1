"""
测试数据库集成和商品匹配功能
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from loguru import logger
from utils.database import init_database, close_database, get_product_database
from utils.product_matcher import init_matcher_from_database, match_products


async def test_database_connection():
    """测试数据库连接"""
    logger.info("测试数据库连接...")
    
    try:
        # 初始化数据库
        await init_database()
        logger.info("✅ 数据库连接成功")
        
        # 获取商品数据库实例
        product_db = get_product_database()
        if not product_db:
            logger.error("❌ 无法获取商品数据库实例")
            return False
            
        # 测试获取商品数据
        products = await product_db.get_all_products()
        logger.info(f"✅ 成功获取 {len(products)} 个商品")
        
        # 显示前几个商品的信息
        for i, product in enumerate(products[:3]):
            logger.info(f"商品 {i+1}: ID={product['id']}, 名称={product['name']}, 匹配名称={product.get('match_names', [])}")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库连接测试失败: {e}")
        return False


async def test_product_matcher():
    """测试商品匹配器"""
    logger.info("测试商品匹配器...")
    
    try:
        # 从数据库初始化匹配器
        await init_matcher_from_database()
        logger.info("✅ 商品匹配器初始化成功")
        
        # 测试文本
        test_texts = [
            "我想要DIMOO心动特调系列",
            "有没有泡泡玛特的商品",
            "SKULLPANDA暗黑童话系列盲盒",
            "想买个毛绒玩具",
            "MOLLY茉莉",
            "这个商品多少钱"
        ]
        
        for text in test_texts:
            logger.info(f"\n测试文本: {text}")
            
            # 进行商品匹配
            matches = match_products(text, max_edits=1)
            
            if matches:
                logger.info(f"✅ 找到 {len(matches)} 个匹配商品:")
                for match in matches:
                    product_id = match.get("商品ID")
                    product_name = match.get("商品名称")
                    match_info = match.get("匹配信息", {})
                    match_pattern = match_info.get("匹配模式", "")
                    position = match_info.get("原始文本中的位置", [])
                    
                    logger.info(f"  - 商品: {product_name} (ID: {product_id})")
                    logger.info(f"    匹配模式: {match_pattern}")
                    logger.info(f"    位置: {position}")
            else:
                logger.info("❌ 未找到匹配的商品")
                
        return True
        
    except Exception as e:
        logger.error(f"❌ 商品匹配器测试失败: {e}")
        return False


async def test_specific_product():
    """测试特定商品查询"""
    logger.info("测试特定商品查询...")
    
    try:
        product_db = get_product_database()
        if not product_db:
            logger.error("❌ 无法获取商品数据库实例")
            return False
            
        # 测试根据ID查询商品
        test_product_id = 1  # 假设存在ID为1的商品
        product = await product_db.get_product_by_id(test_product_id)
        
        if product:
            logger.info(f"✅ 找到商品 ID {test_product_id}:")
            logger.info(f"  名称: {product['name']}")
            logger.info(f"  昵称: {product.get('nick_name', 'N/A')}")
            logger.info(f"  匹配名称: {product.get('match_names', [])}")
            logger.info(f"  系列信息: {product.get('series', 'N/A')}")
        else:
            logger.info(f"❌ 未找到 ID 为 {test_product_id} 的商品")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 特定商品查询测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <5}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    logger.info("🚀 开始数据库集成测试")
    
    try:
        # 测试数据库连接
        if not await test_database_connection():
            logger.error("❌ 数据库连接测试失败，停止后续测试")
            return
            
        # 测试商品匹配器
        if not await test_product_matcher():
            logger.error("❌ 商品匹配器测试失败")
            
        # 测试特定商品查询
        if not await test_specific_product():
            logger.error("❌ 特定商品查询测试失败")
            
        logger.info("🎉 所有测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        
    finally:
        # 关闭数据库连接
        await close_database()
        logger.info("🔒 数据库连接已关闭")


if __name__ == "__main__":
    asyncio.run(main())
