"""
MQ发布器测试脚本
"""
import asyncio
import json
from datetime import datetime, timezone
from utils.mq_publisher import init_product_match_publisher, publish_product_matches, close_product_match_publisher
from loguru import logger

async def test_publisher():
    """测试MQ发布器"""
    try:
        # 初始化发布器
        logger.info("Initializing MQ publisher...")
        await init_product_match_publisher()
        
        # 测试数据
        test_data = {
            "ts": datetime.now(timezone.utc).isoformat(),
            "msg_id": 1234567890,
            "store": {
                "id": 123,
                "name": "泡泡玛特北京朝阳店"
            },
            "products": [
                {
                    "id": 4878,
                    "name": "DIMOO心动特调系列-搪胶毛绒吊卡"
                },
                {
                    "id": 1234,
                    "name": "MOLLY你好，月亮1/8可动人偶"
                }
            ]
        }
        
        # 发布测试消息
        logger.info("Publishing test message...")
        success = await publish_product_matches(
            ts=test_data["ts"],
            msg_id=test_data["msg_id"],
            store=test_data["store"],
            products=test_data["products"]
        )
        
        if success:
            logger.info("✅ Test message published successfully!")
            logger.info(f"Test data: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
        else:
            logger.error("❌ Failed to publish test message")
            
    except Exception as e:
        logger.error(f"Test failed: {e}")
    finally:
        # 关闭发布器
        logger.info("Closing MQ publisher...")
        await close_product_match_publisher()

if __name__ == "__main__":
    # 配置日志
    logger.add("logs/test_publisher.log", rotation="1 day", retention="7 days")
    
    # 运行测试
    asyncio.run(test_publisher())
