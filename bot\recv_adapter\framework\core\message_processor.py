"""
消息处理器
"""
import time
from typing import Dict, Any
from loguru import logger

from .models import StandardMessage


class MessageProcessor:
    """消息处理器"""

    def _determine_message_name_v1_5_v2(self, data: Dict[str, Any]) -> str:
        """确定V1.5和V2消息的msg_name"""
        # 对于V1.5，直接检查顶级字段
        if "AddMsgs" in data and data["AddMsgs"]:
            return "AddMsgs"
        elif "ModContacts" in data and data["ModContacts"]:
            return "ModContacts"
        elif "DelContacts" in data and data["DelContacts"]:
            return "DelContacts"
        elif "ModUserInfos" in data and data["ModUserInfos"]:
            return "ModUserInfos"
        elif "ModUserImgs" in data and data["ModUserImgs"]:
            return "ModUserImgs"
        elif "FunctionSwitchs" in data and data["FunctionSwitchs"]:
            return "FunctionSwitchs"
        elif "UserInfoExts" in data and data["UserInfoExts"]:
            return "UserInfoExts"

        # 对于V2，检查Data字段内的字段
        data_obj = data.get("Data")
        # Data 可能为 None 或非字典（例如接口错误/掉线），此时视为未知类型
        if not isinstance(data_obj, dict) or not data_obj:
            return "Unknown"
        if "AddMsgs" in data_obj and data_obj.get("AddMsgs"):
            return "AddMsgs"
        elif "ModContacts" in data_obj and data_obj.get("ModContacts"):
            return "ModContacts"
        elif "DelContacts" in data_obj and data_obj.get("DelContacts"):
            return "DelContacts"
        elif "ModUserInfos" in data_obj and data_obj.get("ModUserInfos"):
            return "ModUserInfos"
        elif "ModUserImgs" in data_obj and data_obj.get("ModUserImgs"):
            return "ModUserImgs"
        elif "FunctionSwitchs" in data_obj and data_obj.get("FunctionSwitchs"):
            return "FunctionSwitchs"
        elif "UserInfoExts" in data_obj and data_obj.get("UserInfoExts"):
            return "UserInfoExts"

        return "Unknown"
    
    def normalize_message_v1(self, data: Dict[str, Any]) -> StandardMessage:
        """标准化版本1消息 (wechatpadpro)"""
        msg_type = data.get("msg_type", 0)

        # 确定消息名称
        if msg_type == 10000:
            msg_name = "ModContacts"  # V1中msg_type=10000表示ModContacts
        else:
            msg_name = "AddMsgs"  # V1没有msg_name，全部作为AddMsgs处理

        return StandardMessage(
            msg_name=msg_name,
            msg_id=data.get("msg_id", 0),
            msg_type=msg_type,
            timestamp=data.get("create_time", 0),
            is_self_message=data.get("is_self_message", False),
            wxid=data.get("account_wxid", ""),
            uuid=data.get("account_uuid", ""),
            from_user_name=data.get("from_user_name", ""),
            to_user_name=data.get("to_user_name", ""),
            content=data.get("content", ""),
            push_content=data.get("push_content", ""),
            msg_source=data.get("msg_source", ""),
            ver=1,
            raw_data=data
        )
    
    def normalize_message_v1_5(self, data: Dict[str, Any]) -> StandardMessage:
        """标准化版本1.5消息 (wx_msg)"""
        # V1.5和V2一样，判断Data字段中哪些字段非NULL来确定消息类型
        msg_name = self._determine_message_name_v1_5_v2(data)

        add_msgs = data.get("AddMsgs", [])
        if not add_msgs:
            raise ValueError("No AddMsgs found in message")

        first_msg = add_msgs[0]
        user_name = data.get("userName", "")

        return StandardMessage(
            msg_name=msg_name,
            msg_id=first_msg.get("msg_id", 0),
            msg_type=first_msg.get("msg_type", 0),
            timestamp=first_msg.get("create_time", 0),
            is_self_message=first_msg.get("from_user_name", {}).get("str", "") == user_name,
            wxid=user_name,
            uuid=data.get("UUID", ""),
            from_user_name=first_msg.get("from_user_name", {}).get("str", ""),
            to_user_name=first_msg.get("to_user_name", {}).get("str", ""),
            content=first_msg.get("content", {}).get("str", ""),
            push_content=first_msg.get("push_content", ""),
            msg_source=first_msg.get("msg_source", ""),
            ver=15,
            raw_data=data
        )
    
    def normalize_message_v2(self, data: Dict[str, Any]) -> StandardMessage:
        """标准化版本2消息 (wxapi)"""
        # 如果API返回失败，直接抛出更清晰的异常
        if data.get("Success") is False:
            raise ValueError(f"V2 API error: Code={data.get('Code')}, Message={data.get('Message')}")

        # V2：判断Data字段中哪些字段非NULL来确定消息类型
        msg_name = self._determine_message_name_v1_5_v2(data)
        if msg_name == "Unknown":
            raise ValueError("Unknown message type in V2 Data")

        data_obj = data.get("Data") or {}
        if not isinstance(data_obj, dict):
            raise ValueError(f"Invalid V2 Data payload: expected object, got {type(data.get('Data'))}")
        add_msgs = data_obj.get("AddMsgs", [])
        if not add_msgs:
            raise ValueError("No AddMsgs found in Data")

        first_msg = add_msgs[0]
        wxid = data_obj.get("Wxid", "")

        return StandardMessage(
            msg_name=msg_name,
            msg_id=first_msg.get("MsgId", 0),
            msg_type=first_msg.get("MsgType", 0),
            timestamp=first_msg.get("CreateTime", 0),
            is_self_message=first_msg.get("FromUserName", {}).get("string", "") == wxid,
            wxid=wxid,
            uuid="",  # 版本2没有UUID
            from_user_name=first_msg.get("FromUserName", {}).get("string", ""),
            to_user_name=first_msg.get("ToUserName", {}).get("string", ""),
            content=first_msg.get("Content", {}).get("string", ""),
            push_content=first_msg.get("PushContent", ""),
            msg_source=first_msg.get("MsgSource", ""),
            ver=2,
            raw_data=data
        )
    
    def normalize_message(self, data: Dict[str, Any], queue_name: str, version: int) -> StandardMessage:
        """根据版本标准化消息"""
        try:
            if version == 1:
                if queue_name == "wx_msg":
                    return self.normalize_message_v1_5(data)
                else:
                    return self.normalize_message_v1(data)
            elif version == 2:
                return self.normalize_message_v2(data)
            elif version == 3:
                return self.normalize_message_v3(data)
            else:
                raise ValueError(f"Unsupported message version: {version}")
        except Exception as e:
            logger.debug(f"Failed to normalize message from {queue_name} (v{version}): {e}\n{data}")
            logger.debug(f"Raw data: {data}")
            return None

    def normalize_message_v3(self, data: Dict[str, Any]) -> StandardMessage:
        """标准化版本3消息 (webhook)"""
        # V3 webhook：先判断msg_name，再处理具体内容
        type_name = data.get("TypeName", "AddMsg")

        # 将TypeName映射到标准的msg_name
        msg_name_mapping = {
            "AddMsg": "AddMsgs",
            "ModContacts": "ModContacts",
            "DelContacts": "DelContacts",
            "Offline": "Offline"
        }
        msg_name = msg_name_mapping.get(type_name, "AddMsgs")

        # 提取基本信息
        appid = data.get("Appid", "")
        wxid = data.get("Wxid", "")
        msg_data = data.get("Data", {})

        # 统一处理所有V3消息类型
        # 提取消息字段（如果存在）
        msg_id = msg_data.get("MsgId", 0)
        msg_type = msg_data.get("MsgType", 0)
        create_time = msg_data.get("CreateTime", int(time.time()))

        # 提取用户信息
        from_user = msg_data.get("FromUserName", {}).get("string", "")
        to_user = msg_data.get("ToUserName", {}).get("string", "")

        # 如果没有用户信息，尝试从其他字段获取
        if not from_user:
            from_user = msg_data.get("UserName", {}).get("string", wxid)
        if not to_user:
            to_user = wxid

        # 提取消息内容
        content = msg_data.get("Content", {}).get("string", "")

        # 如果没有内容，根据消息类型生成描述性内容
        if not content:
            if type_name == "ModContacts":
                nick_name = msg_data.get("NickName", {}).get("string", "")
                content = f"联系人变更: {nick_name} ({from_user})"
            elif type_name == "DelContacts":
                content = f"删除联系人: {from_user}"
            elif type_name == "Offline":
                content = f"微信账号掉线: {wxid}"

        push_content = msg_data.get("PushContent", "")
        msg_source = msg_data.get("MsgSource", "")

        # 判断是否是自己发送的消息
        is_self_message = from_user == wxid

        return StandardMessage(
            msg_name=msg_name,
            msg_id=msg_id,
            msg_type=msg_type,
            timestamp=create_time,
            is_self_message=is_self_message,
            wxid=wxid,
            uuid=appid,
            from_user_name=from_user,
            to_user_name=to_user,
            content=content,
            push_content=push_content,
            msg_source=msg_source,
            ver=3,
            raw_data=data
        )


