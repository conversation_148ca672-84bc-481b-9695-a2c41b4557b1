# 商品匹配结果MQ发布功能

## 概述

本功能将商品匹配结果发布到RabbitMQ交换机，供多个订阅程序消费。每当在群消息中识别到商品时，会自动发布包含店铺信息和商品列表的事件到MQ。

## 架构设计

- **交换机**: `events.product-matches` (Topic类型，持久化)
- **路由键**: `product.matches.store.{store_id}`
- **消息格式**: JSON，包含时间戳、消息ID、店铺信息、商品列表

## 消息格式

```json
{
  "ts": "2025-08-15T08:00:00.123456Z",
  "msg_id": 1234567890,
  "store": {
    "id": 123,
    "name": "泡泡玛特北京朝阳店"
  },
  "products": [
    {
      "id": 4878,
      "name": "DIMOO心动特调系列-搪胶毛绒吊卡"
    },
    {
      "id": 1234,
      "name": "MOLLY你好，月亮1/8可动人偶"
    }
  ]
}
```

## 队列保护策略

为防止订阅者离线导致队列积压，建议为订阅队列设置以下策略：

- **消息TTL**: 5分钟 (300,000毫秒)
- **队列长度限制**: 50,000条消息
- **溢出策略**: drop-head (丢弃最老消息)
- **死信交换机**: dlx.product-matches
- **队列闲置过期**: 7天

## 使用方法

### 1. 启动发布器

发布器会在主程序启动时自动初始化，无需手动操作。

### 2. 创建订阅者

参考 `subscriber_example.py` 创建你的订阅者：

```python
import aio_pika
import json
import asyncio

async def consume_product_matches(app_name: str = "myapp"):
    conn = await aio_pika.connect_robust("amqp://user:pass@host:5672/")
    ch = await conn.channel()
    
    # 声明交换机
    ex = await ch.declare_exchange("events.product-matches", aio_pika.ExchangeType.TOPIC, durable=True)
    
    # 声明队列（带保护策略）
    queue_name = f"{app_name}.product-matches"
    args = {
        "x-message-ttl": 300000,
        "x-max-length": 50000,
        "x-overflow": "drop-head",
        "x-dead-letter-exchange": "dlx.product-matches",
        "x-dead-letter-routing-key": "dlq.product-matches",
        "x-expires": 7 * 24 * 3600 * 1000
    }
    q = await ch.declare_queue(queue_name, durable=True, arguments=args)
    
    # 绑定队列
    await q.bind(ex, routing_key="product.matches.store.*")
    
    # 消费消息
    async with q.iterator() as it:
        async for m in it:
            async with m.process():
                event = json.loads(m.body.decode("utf-8"))
                # 处理事件...
```

### 3. 设置RabbitMQ策略（推荐）

运行策略设置脚本：

```bash
chmod +x setup_rabbitmq_policies.sh
./setup_rabbitmq_policies.sh
```

或手动执行：

```bash
# 消息TTL
rabbitmqctl set_policy TTL ".*\.product-matches.*" \
'{"message-ttl":300000}' --apply-to queues

# 队列长度限制
rabbitmqctl set_policy LIMIT ".*\.product-matches.*" \
'{"max-length":50000,"overflow":"drop-head"}' --apply-to queues

# 死信交换机
rabbitmqctl set_policy DLX ".*\.product-matches.*" \
'{"dead-letter-exchange":"dlx.product-matches","dead-letter-routing-key":"dlq.product-matches"}' --apply-to queues

# 队列过期
rabbitmqctl set_policy EXPIRES ".*\.product-matches.*" \
'{"expires":*********}' --apply-to queues
```

## 监控和运维

### 查看队列状态

```bash
# 查看所有队列
rabbitmqctl list_queues name messages consumers

# 查看特定队列
rabbitmqctl list_queues name messages consumers | grep product-matches

# 查看死信队列
rabbitmqctl list_queues name messages | grep dlq
```

### 查看策略

```bash
rabbitmqctl list_policies
```

### 清理队列（谨慎操作）

```bash
# 清空特定队列
rabbitmqctl purge_queue "appX.product-matches"

# 删除队列
rabbitmqctl delete_queue "appX.product-matches"
```

## 故障排查

### 1. 发布失败

检查日志中的错误信息：
- RabbitMQ连接是否正常
- 交换机是否存在
- 权限是否足够

### 2. 消息丢失

检查：
- 消息是否设置了持久化
- 队列是否设置了持久化
- TTL是否过短

### 3. 队列积压

检查：
- 订阅者是否在线
- 消费速度是否跟上发布速度
- 是否需要增加消费者实例

## 扩展建议

1. **按店铺过滤**: 使用路由键 `product.matches.store.{store_id}` 只订阅特定店铺
2. **批量处理**: 在订阅者中实现批量处理提高性能
3. **重试机制**: 对处理失败的消息实现重试
4. **监控告警**: 监控队列长度、消费延迟等指标
5. **数据持久化**: 将重要事件持久化到数据库备份
