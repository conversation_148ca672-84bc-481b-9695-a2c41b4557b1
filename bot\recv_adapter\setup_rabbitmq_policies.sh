#!/bin/bash

# RabbitMQ队列策略设置脚本
# 为商品匹配结果队列设置TTL/长度/溢出/DLX策略

echo "Setting up RabbitMQ policies for product match queues..."

# 设置消息TTL（5分钟）
echo "Setting message TTL policy..."
rabbitmqctl set_policy TTL ".*\.product-matches.*" \
'{"message-ttl":300000}' --apply-to queues

# 设置队列长度限制和溢出策略
echo "Setting queue length limit policy..."
rabbitmqctl set_policy LIMIT ".*\.product-matches.*" \
'{"max-length":50000,"overflow":"drop-head"}' --apply-to queues

# 设置死信交换机
echo "Setting dead letter exchange policy..."
rabbitmqctl set_policy DLX ".*\.product-matches.*" \
'{"dead-letter-exchange":"dlx.product-matches","dead-letter-routing-key":"dlq.product-matches"}' --apply-to queues

# 设置队列闲置过期（7天）
echo "Setting queue expires policy..."
rabbitmqctl set_policy EXPIRES ".*\.product-matches.*" \
'{"expires":*********}' --apply-to queues

echo "RabbitMQ policies setup completed!"

# 查看设置的策略
echo "Current policies:"
rabbitmqctl list_policies
