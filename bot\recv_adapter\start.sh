#!/bin/bash

# 微信机器人消息接收处理程序启动脚本

echo "Starting WeChat Bot Receiver..."

# 检查.env文件
if [ ! -f ".env" ]; then
    echo "Error: .env file not found"
    echo "Please copy .env.example to .env and configure it:"
    echo "cp .env.example .env"
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 检查依赖
echo "Checking dependencies..."
if ! python3.12 -c "import aio_pika, aiohttp, pydantic, loguru" 2>/dev/null; then
    echo "Installing dependencies..."
    pip3 install -r requirements.txt
fi

# 启动程序
echo "Starting the program..."
python3.12 run.py
