"""
数据库连接和操作模块

提供MySQL数据库连接池管理和商品数据获取功能
"""
import asyncio
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
import aiomysql
from loguru import logger

from config import settings


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self._pool: Optional[aiomysql.Pool] = None
        
    async def init_pool(self):
        """初始化数据库连接池"""
        try:
            self._pool = await aiomysql.create_pool(
                host=settings.mysql_host,
                port=settings.mysql_port,
                user=settings.mysql_user,
                password=settings.mysql_password,
                db=settings.mysql_database,
                charset=settings.mysql_charset,
                autocommit=True,
                minsize=1,
                maxsize=10,
                echo=False
            )
            logger.info("数据库连接池初始化成功")
        except Exception as e:
            logger.error(f"数据库连接池初始化失败: {e}")
            raise
            
    async def close_pool(self):
        """关闭数据库连接池"""
        if self._pool:
            self._pool.close()
            await self._pool.wait_closed()
            logger.info("数据库连接池已关闭")
            
    async def get_connection(self):
        """获取数据库连接"""
        if not self._pool:
            await self.init_pool()
        return await self._pool.acquire()
        
    async def release_connection(self, conn):
        """释放数据库连接"""
        if self._pool:
            await self._pool.release(conn)


class ProductDatabase:
    """商品数据库操作类"""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager


class StoreDatabase:
    """店铺数据库操作类"""

    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        
    async def get_all_products(self) -> List[Dict[str, Any]]:
        """
        获取所有商品数据
        
        Returns:
            商品列表，格式：
            [
                {
                    "id": 123,
                    "name": "商品名称",
                    "nick_name": "显示昵称",
                    "match_names": ["匹配名称1", "匹配名称2"],
                    "series": {"key": "系列名称", "variant": "系列品类"},
                    "product_img": "图片URL",
                    "cdn_info": {...},
                    "created_at": datetime
                },
                ...
            ]
        """
        conn = None
        try:
            conn = await self.db_manager.get_connection()
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                sql = """
                SELECT 
                    product_id,
                    original_name,
                    nick_name,
                    product_img,
                    match_names,
                    series_key,
                    series_variant,
                    cdn_info,
                    cdn_update,
                    created_at
                FROM products
                ORDER BY product_id
                """
                await cursor.execute(sql)
                rows = await cursor.fetchall()
                
                products = []
                for row in rows:
                    # 解析JSON字段
                    match_names = []
                    if row['match_names']:
                        try:
                            match_names = json.loads(row['match_names'])
                            if not isinstance(match_names, list):
                                match_names = []
                        except (json.JSONDecodeError, TypeError):
                            match_names = []
                    
                    cdn_info = {}
                    if row['cdn_info']:
                        try:
                            cdn_info = json.loads(row['cdn_info'])
                            if not isinstance(cdn_info, dict):
                                cdn_info = {}
                        except (json.JSONDecodeError, TypeError):
                            cdn_info = {}
                    
                    # 构建商品数据
                    product = {
                        "id": row['product_id'],
                        "name": row['original_name'] or "",
                        "nick_name": row['nick_name'] or "",
                        "match_names": match_names,
                        "product_img": row['product_img'] or "",
                        "cdn_info": cdn_info,
                        "cdn_update": row['cdn_update'],
                        "created_at": row['created_at']
                    }
                    
                    # 添加系列信息
                    if row['series_key'] or row['series_variant']:
                        product["series"] = {
                            "key": row['series_key'] or "",
                            "variant": row['series_variant'] or ""
                        }
                    
                    products.append(product)
                
                logger.info(f"成功获取 {len(products)} 个商品数据")
                return products
                
        except Exception as e:
            logger.error(f"获取商品数据失败: {e}")
            raise
        finally:
            if conn:
                await self.db_manager.release_connection(conn)
                
    async def get_product_by_id(self, product_id: int) -> Optional[Dict[str, Any]]:
        """
        根据商品ID获取单个商品数据
        
        Args:
            product_id: 商品ID
            
        Returns:
            商品数据字典，如果不存在则返回None
        """
        conn = None
        try:
            conn = await self.db_manager.get_connection()
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                sql = """
                SELECT 
                    product_id,
                    original_name,
                    nick_name,
                    product_img,
                    match_names,
                    series_key,
                    series_variant,
                    cdn_info,
                    cdn_update,
                    created_at
                FROM products
                WHERE product_id = %s
                """
                await cursor.execute(sql, (product_id,))
                row = await cursor.fetchone()
                
                if not row:
                    return None
                
                # 解析JSON字段
                match_names = []
                if row['match_names']:
                    try:
                        match_names = json.loads(row['match_names'])
                        if not isinstance(match_names, list):
                            match_names = []
                    except (json.JSONDecodeError, TypeError):
                        match_names = []
                
                cdn_info = {}
                if row['cdn_info']:
                    try:
                        cdn_info = json.loads(row['cdn_info'])
                        if not isinstance(cdn_info, dict):
                            cdn_info = {}
                    except (json.JSONDecodeError, TypeError):
                        cdn_info = {}
                
                # 构建商品数据
                product = {
                    "id": row['product_id'],
                    "name": row['original_name'] or "",
                    "nick_name": row['nick_name'] or "",
                    "match_names": match_names,
                    "product_img": row['product_img'] or "",
                    "cdn_info": cdn_info,
                    "cdn_update": row['cdn_update'],
                    "created_at": row['created_at']
                }
                
                # 添加系列信息
                if row['series_key'] or row['series_variant']:
                    product["series"] = {
                        "key": row['series_key'] or "",
                        "variant": row['series_variant'] or ""
                    }
                
                return product
                
        except Exception as e:
            logger.error(f"获取商品数据失败 (ID: {product_id}): {e}")
            raise
        finally:
            if conn:
                await self.db_manager.release_connection(conn)

    async def get_store_by_group_id(self, group_id: str) -> Optional[Dict[str, Any]]:
        """
        通过群ID查询店铺信息（使用多值索引）

        Args:
            group_id: 群ID

        Returns:
            店铺数据字典，如果不存在则返回None
        """
        if not group_id:
            return None

        conn = None
        try:
            conn = await self.db_manager.get_connection()
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                sql = """
                SELECT
                    store_id,
                    store_name,
                    match_names,
                    group_ids,
                    location,
                    opening,
                    created_at
                FROM stores
                WHERE %s MEMBER OF (group_ids) AND opening = 1
                LIMIT 1
                """
                await cursor.execute(sql, (group_id,))
                row = await cursor.fetchone()

                if not row:
                    return None

                # 解析JSON字段
                match_names = []
                if row['match_names']:
                    try:
                        match_names = json.loads(row['match_names'])
                        if not isinstance(match_names, list):
                            match_names = []
                    except (json.JSONDecodeError, TypeError):
                        match_names = []

                group_ids = []
                if row['group_ids']:
                    try:
                        group_ids = json.loads(row['group_ids'])
                        if not isinstance(group_ids, list):
                            group_ids = []
                    except (json.JSONDecodeError, TypeError):
                        group_ids = []

                # 构建店铺数据
                store = {
                    "id": row['store_id'],
                    "name": row['store_name'] or "",
                    "match_names": match_names,
                    "group_ids": group_ids,
                    "location": row['location'] or "",
                    "opening": bool(row['opening']) if row['opening'] is not None else True,
                    "created_at": row['created_at']
                }

                return store

        except Exception as e:
            logger.error(f"通过群ID查询店铺失败 (group_id: {group_id}): {e}")
            raise
        finally:
            if conn:
                await self.db_manager.release_connection(conn)

    async def get_all_stores(self) -> List[Dict[str, Any]]:
        """
        获取所有店铺数据

        Returns:
            店铺列表，格式：
            [
                {
                    "id": 123,
                    "name": "店铺名称",
                    "match_names": ["匹配名称1", "匹配名称2"],
                    "group_ids": ["群ID1", "群ID2"],
                    "location": "地区",
                    "opening": True,
                    "created_at": datetime
                },
                ...
            ]
        """
        conn = None
        try:
            conn = await self.db_manager.get_connection()
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                sql = """
                SELECT
                    store_id,
                    store_name,
                    match_names,
                    group_ids,
                    location,
                    opening,
                    created_at
                FROM stores
                WHERE opening = 1
                ORDER BY store_id
                """
                await cursor.execute(sql)
                rows = await cursor.fetchall()

                stores = []
                for row in rows:
                    # 解析JSON字段
                    match_names = []
                    if row['match_names']:
                        try:
                            match_names = json.loads(row['match_names'])
                            if not isinstance(match_names, list):
                                match_names = []
                        except (json.JSONDecodeError, TypeError):
                            match_names = []

                    group_ids = []
                    if row['group_ids']:
                        try:
                            group_ids = json.loads(row['group_ids'])
                            if not isinstance(group_ids, list):
                                group_ids = []
                        except (json.JSONDecodeError, TypeError):
                            group_ids = []

                    # 构建店铺数据
                    store = {
                        "id": row['store_id'],
                        "name": row['store_name'] or "",
                        "match_names": match_names,
                        "group_ids": group_ids,
                        "location": row['location'] or "",
                        "opening": bool(row['opening']) if row['opening'] is not None else True,
                        "created_at": row['created_at']
                    }

                    stores.append(store)

                logger.info(f"成功获取 {len(stores)} 个店铺数据")
                return stores

        except Exception as e:
            logger.error(f"获取店铺数据失败: {e}")
            raise
        finally:
            if conn:
                await self.db_manager.release_connection(conn)


# 全局数据库管理器实例
_db_manager: Optional[DatabaseManager] = None
_product_db: Optional[ProductDatabase] = None
_store_db: Optional[StoreDatabase] = None


async def init_database():
    """初始化数据库连接"""
    global _db_manager, _product_db, _store_db

    _db_manager = DatabaseManager()
    await _db_manager.init_pool()
    _product_db = ProductDatabase(_db_manager)
    _store_db = StoreDatabase(_db_manager)

    logger.info("数据库模块初始化完成")


async def close_database():
    """关闭数据库连接"""
    global _db_manager, _store_db

    if _db_manager:
        await _db_manager.close_pool()
        _db_manager = None
        _store_db = None

    logger.info("数据库模块已关闭")


def get_product_database() -> Optional[ProductDatabase]:
    """获取商品数据库操作实例"""
    return _product_db


def get_store_database() -> Optional[StoreDatabase]:
    """获取店铺数据库操作实例"""
    return _store_db


__all__ = [
    'DatabaseManager',
    'ProductDatabase',
    'StoreDatabase',
    'init_database',
    'close_database',
    'get_product_database',
    'get_store_database'
]
