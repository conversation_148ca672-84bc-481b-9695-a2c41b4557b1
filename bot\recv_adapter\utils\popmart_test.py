"""
泡泡玛特商品匹配器测试

使用你提供的真实商品数据和文本进行测试
"""

from popmart_product_matcher import PopmartProductMatcher, convert_popmart_data

def test_popmart_matcher():
    # 你提供的商品数据
    raw_products = [
        "迪士尼公主创想世界系列手办",
        "KUBO日落之城系列手办", 
        "浪漫指尖系列4场景手办",
        "CRYBABY 真爱之盒手办",
        "DIMOO心动特调系列-搪胶毛绒吊卡，DIMOO心动特调系列",
        "MOLLY你好，月亮1/8可动人偶",
        "DIMOO心动特调系列-软脸毛绒钥匙扣盲盒，DIMOO心动特调系列，钥匙扣",
        "DIMOO心动特调系列-水晶球，DIMOO心动特调系列，水晶球",
        "DIMOO心动特调系列-咖啡杯，DIMOO心动特调系列，咖啡杯",
        "DIMOO心动特调系列-盲盒亚克力冰箱贴夹子，DIMOO心动特调系列，冰箱贴",
        "DIMOO心动特调系列-盲盒手机挂链，DIMOO心动特调系列，手机链",
        "DIMOO心动特调系列-香包挂件套装，DIMOO心动特调系列，香包",
        "CRYBABY悲伤俱乐部系列-硅胶毛绒耳机包，CRYBABY悲伤俱乐部系列，耳机包",
        "HACIPUPU史迪奇1/8可动人偶",
        "CRYBABY SHINY SHINY系列-卡套盲盒，CRYBABY SHINY SHINY系列，卡套",
        "Hirono 钢琴师手办"
    ]
    
    # 转换数据格式
    product_catalog = convert_popmart_data(raw_products)
    
    print("转换后的商品目录（前5个）：")
    for i, product in enumerate(product_catalog[:5]):
        print(f"{i+1}. ID:{product['id']} 名称:{product['name']}")
        if product['series']:
            print(f"   系列:{product['series']}")
        if product['sub_product']:
            print(f"   子商品:{product['sub_product']}")
        print()
    
    # 创建匹配器
    matcher = PopmartProductMatcher(product_catalog)
    
    # 测试文本1：复杂的系列表达式
    test_text1 = """🎈衍生品：DIMOO心动特调系列-衍生品【软脸毛绒钥匙扣盲盒/水晶球/咖啡杯/盲盒亚克力冰箱贴夹子/盲盒手机挂链/香包挂件套装】"""
    
    print("=" * 60)
    print("测试1：复杂系列表达式")
    print("=" * 60)
    print(f"文本: {test_text1}")
    print()
    
    results1 = matcher.match(test_text1, max_edits=1)
    print(f"匹配到 {len(results1)} 个商品:")
    for result in results1:
        print(f"- 商品ID: {result['商品ID']}")
        print(f"  商品名称: {result['商品名称']}")
        print(f"  匹配模式: {result['匹配信息']['匹配模式']}")
        print(f"  位置: {result['匹配信息']['原始文本中的位置']}")
        print()
    
    # 测试文本2：分号分隔的多个商品
    test_text2 = """🎈盲盒手办：迪士尼公主创想世界系列手办；KUBO日落之城系列手办；浪漫指尖系列4场景手办；"""
    
    print("=" * 60)
    print("测试2：分号分隔的多个商品")
    print("=" * 60)
    print(f"文本: {test_text2}")
    print()
    
    results2 = matcher.match(test_text2, max_edits=1)
    print(f"匹配到 {len(results2)} 个商品:")
    for result in results2:
        print(f"- 商品ID: {result['商品ID']}")
        print(f"  商品名称: {result['商品名称']}")
        print(f"  匹配模式: {result['匹配信息']['匹配模式']}")
        print(f"  位置: {result['匹配信息']['原始文本中的位置']}")
        print()
    
    # 测试文本3：具体的系列-子商品表达
    test_text3 = """📍CRYBABY 真爱之盒手办；DIMOO心动特调系列-搪胶毛绒吊卡；MOLLY你好,月亮1/8可动人偶；DIMOO心动特调系列-衍生品【软脸毛绒钥匙扣盲盒/咖啡杯】"""
    
    print("=" * 60)
    print("测试3：混合表达式")
    print("=" * 60)
    print(f"文本: {test_text3}")
    print()
    
    results3 = matcher.match(test_text3, max_edits=1)
    print(f"匹配到 {len(results3)} 个商品:")
    for result in results3:
        print(f"- 商品ID: {result['商品ID']}")
        print(f"  商品名称: {result['商品名称']}")
        print(f"  匹配模式: {result['匹配信息']['匹配模式']}")
        print(f"  位置: {result['匹配信息']['原始文本中的位置']}")
        print()
    
    # 测试文本4：简单列举
    test_text4 = """1️⃣CRYBABY悲伤俱乐部系列-硅胶毛绒耳机包(线上线下同步售卖）
2️⃣HACIPUPU史迪奇1/8可动人偶（仅线上小程序售卖，不支持代取❗️）
3️⃣CRYBABY SHINY SHINY系列-卡套盲盒（仅线上小程序售卖，不支持代取❗️）
4️⃣Hirono 钢琴师手办"""
    
    print("=" * 60)
    print("测试4：简单列举")
    print("=" * 60)
    print(f"文本: {test_text4}")
    print()
    
    results4 = matcher.match(test_text4, max_edits=1)
    print(f"匹配到 {len(results4)} 个商品:")
    for result in results4:
        print(f"- 商品ID: {result['商品ID']}")
        print(f"  商品名称: {result['商品名称']}")
        print(f"  匹配模式: {result['匹配信息']['匹配模式']}")
        print(f"  位置: {result['匹配信息']['原始文本中的位置']}")
        print()
    
    print("=" * 60)
    print("总结")
    print("=" * 60)
    print("泡泡玛特专用匹配器特点：")
    print("1. 处理复杂的中括号衍生品表达：【软脸毛绒钥匙扣盲盒/水晶球/咖啡杯】")
    print("2. 识别系列-子商品结构：DIMOO心动特调系列-搪胶毛绒吊卡")
    print("3. 支持分号分隔的多商品列表")
    print("4. 保持原有的模糊匹配和错字纠正功能")
    print("5. 返回标准格式：商品ID、商品名称、匹配信息")

if __name__ == "__main__":
    test_popmart_matcher()
