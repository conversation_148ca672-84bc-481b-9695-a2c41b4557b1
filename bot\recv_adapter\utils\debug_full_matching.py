"""
调试完整匹配流程，检查为什么衍生品没有出现在最终结果中
"""

from product_matcher import ProductMatcher, _parse_series_expressions

def debug_full_matching():
    print("=" * 60)
    print("调试完整匹配流程")
    print("=" * 60)
    
    # 完整的测试商品数据
    test_products = [
        {
            "id": 4869,
            "name": "迪士尼公主创想世界系列手办",
            "match_names": ["迪士尼公主创想世界系列手办"],
            "series": {"key": "迪士尼公主创想世界系列", "variant": ""}
        },
        {
            "id": 4870,
            "name": "KUBO日落之城系列手办",
            "match_names": ["KUBO日落之城系列手办"],
            "series": {"key": "KUBO日落之城系列", "variant": ""}
        },
        {
            "id": 4883,
            "name": "浪漫指尖系列4场景手办",
            "match_names": ["浪漫指尖系列4场景手办"],
            "series": {"key": "浪漫指尖系列4", "variant": ""}
        },
        {
            "id": 4868,
            "name": "CRYBABY 真爱之盒手办",
            "match_names": ["CRYBABY真爱之盒手办"],
            "series": {"key": "", "variant": ""}
        },
        {
            "id": 4878,
            "name": "DIMOO心动特调系列-搪胶毛绒吊卡",
            "match_names": ["DIMOO心动特调系列-搪胶毛绒吊卡"],
            "series": {"key": "DIMOO心动特调系列", "variant": "吊卡"}
        },
        {
            "id": 4859,
            "name": "MOLLY你好，月亮1/8可动人偶",
            "match_names": ["MOLLY你好月亮18可动人偶"],
            "series": {"key": "", "variant": ""}
        },
        {
            "id": 4880,
            "name": "DIMOO心动特调系列-软脸毛绒钥匙扣盲盒",
            "match_names": ["DIMOO心动特调系列-软脸毛绒钥匙扣盲盒"],
            "series": {"key": "DIMOO心动特调系列", "variant": "钥匙扣"}
        },
        {
            "id": 4881,
            "name": "DIMOO心动特调系列-水晶球",
            "match_names": ["DIMOO心动特调系列-水晶球"],
            "series": {"key": "DIMOO心动特调系列", "variant": "水晶球"}
        },
        {
            "id": 4882,
            "name": "DIMOO心动特调系列-咖啡杯",
            "match_names": ["DIMOO心动特调系列-咖啡杯"],
            "series": {"key": "DIMOO心动特调系列", "variant": "咖啡杯"}
        },
        {
            "id": 4884,
            "name": "DIMOO心动特调系列-盲盒亚克力冰箱贴夹子",
            "match_names": ["DIMOO心动特调系列-盲盒亚克力冰箱贴夹子"],
            "series": {"key": "DIMOO心动特调系列", "variant": "冰箱贴"}
        },
        {
            "id": 4885,
            "name": "DIMOO心动特调系列-盲盒手机挂链",
            "match_names": ["DIMOO心动特调系列-盲盒手机挂链"],
            "series": {"key": "DIMOO心动特调系列", "variant": "手机"}
        },
        {
            "id": 4886,
            "name": "DIMOO心动特调系列-香包挂件套装",
            "match_names": ["DIMOO心动特调系列-香包挂件套装"],
            "series": {"key": "DIMOO心动特调系列", "variant": "香包"}
        }
    ]
    
    matcher = ProductMatcher(test_products)
    
    # 简化的测试文本，包含中括号表达式
    test_text = """🔔发售产品：
🎈盲盒手办：迪士尼公主创想世界系列手办；KUBO日落之城系列手办；浪漫指尖系列4场景手办；
🎈大号&吊卡&BJD手办：CRYBABY 真爱之盒手办；DIMOO心动特调系列-搪胶毛绒吊卡；MOLLY你好，月亮1/8可动人偶；
🎈衍生品：DIMOO心动特调系列-衍生品【软脸毛绒钥匙扣盲盒/水晶球/咖啡杯/盲盒亚克力冰箱贴夹子/盲盒手机挂链/香包挂件套装】"""
    
    print("1. 分步匹配测试")
    print("-" * 40)
    
    from product_matcher import normalize_with_map
    text_norm, idx_map = normalize_with_map(test_text)
    
    # 1. 精确匹配
    exact_matches = matcher.exact_trie.search_all_matches(text_norm)
    print(f"精确匹配: {len(exact_matches)} 个")
    for match in exact_matches:
        print(f"  - {match.product_info.product_id}: {match.product_info.name}")
    
    # 2. 模糊匹配
    fuzzy_matches = matcher._fuzzy_match(text_norm, 1)
    print(f"模糊匹配: {len(fuzzy_matches)} 个")
    for match in fuzzy_matches:
        print(f"  - {match.product_info.product_id}: {match.product_info.name}")
    
    # 3. 系列表达式匹配
    series_matches = _parse_series_expressions(test_text, matcher.series_map)
    print(f"系列表达式匹配: {len(series_matches)} 个")
    for match in series_matches:
        print(f"  - {match.product_info.product_id}: {match.product_info.name}")
        print(f"    模式: {match.pattern}")
        print(f"    位置: [{match.start}, {match.end}]")
    
    print()
    print("2. 合并和去重测试")
    print("-" * 40)
    
    # 4. 合并所有候选
    all_candidates = exact_matches + fuzzy_matches + series_matches
    print(f"合并后总候选: {len(all_candidates)} 个")
    
    # 统计各类型候选数量
    exact_count = len([c for c in all_candidates if c.match_type == 'exact'])
    fuzzy_count = len([c for c in all_candidates if c.match_type == 'fuzzy'])
    series_count = len([c for c in all_candidates if c.match_type == 'series_expand'])
    
    print(f"  - 精确匹配: {exact_count}")
    print(f"  - 模糊匹配: {fuzzy_count}")
    print(f"  - 系列表达式: {series_count}")
    
    # 5. 去重选择
    selected = matcher._select_non_overlapping(all_candidates)
    print(f"去重后选择: {len(selected)} 个")
    
    for candidate in selected:
        print(f"  - {candidate.product_info.product_id}: {candidate.product_info.name}")
        print(f"    类型: {candidate.match_type}")
        print(f"    位置: [{candidate.start}, {candidate.end}]")
    
    print()
    print("3. 最终结果构建测试")
    print("-" * 40)
    
    # 6. 构建最终结果
    results = []
    seen_product_ids = set()
    
    for candidate in selected:
        print(f"处理候选: {candidate.product_info.product_id} - {candidate.product_info.name}")
        
        # 检查是否已处理
        if candidate.product_info.product_id in seen_product_ids:
            print(f"  -> 跳过（已处理）")
            continue
        
        seen_product_ids.add(candidate.product_info.product_id)
        
        # 计算位置
        if candidate.match_type == 'series_expand':
            orig_start, orig_end = candidate.start, candidate.end
        else:
            orig_start, orig_end = matcher._span_to_original(
                candidate.start, candidate.end, idx_map
            )
        
        result = {
            "商品ID": candidate.product_info.product_id,
            "商品名称": candidate.product_info.name,
            "匹配信息": {
                "匹配模式": candidate.pattern,
                "原始文本中的位置": [orig_start, orig_end],
                "错字信息列表": []
            }
        }
        results.append(result)
        print(f"  -> 添加到结果")
    
    print()
    print("4. 最终结果")
    print("-" * 40)
    
    print(f"最终匹配到 {len(results)} 个商品:")
    for result in results:
        print(f"  - {result['商品ID']}: {result['商品名称']}")
    
    print()
    print("5. 对比完整匹配器结果")
    print("-" * 40)
    
    # 使用完整匹配器
    full_results = matcher.match(test_text, max_edits=1)
    print(f"完整匹配器结果: {len(full_results)} 个商品")
    for result in full_results:
        print(f"  - {result['商品ID']}: {result['商品名称']}")
    
    # 检查差异
    manual_ids = set(r['商品ID'] for r in results)
    full_ids = set(r['商品ID'] for r in full_results)
    
    if manual_ids == full_ids:
        print("✅ 手动流程与完整匹配器结果一致")
    else:
        print("❌ 手动流程与完整匹配器结果不一致")
        print(f"手动流程独有: {manual_ids - full_ids}")
        print(f"完整匹配器独有: {full_ids - manual_ids}")


if __name__ == "__main__":
    debug_full_matching()
