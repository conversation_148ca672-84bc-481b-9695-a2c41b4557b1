"""
泡泡玛特商品匹配器 - 针对真实商品数据优化

专门处理泡泡玛特的复杂商品名称和系列表达式：
- 长商品名称：DIMOO心动特调系列-软脸毛绒钥匙扣盲盒
- 复杂系列表达：DIMOO心动特调系列-衍生品【软脸毛绒钥匙扣盲盒/水晶球/咖啡杯】
- 系列与子商品关系处理
"""
from __future__ import annotations

from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional, Any, Set
import re
from collections import defaultdict

# 从原匹配器导入基础功能
from .product_matcher_v2 import (
    normalize_with_map, normalize, _to_half_width, _bounded_levenshtein,
    ProductInfo, MatchCandidate, Trie, TrieNode
)

# ------------------------------
# 泡泡玛特专用表达式解析
# ------------------------------

def _parse_popmart_series_expressions(text: str, series_map: Dict[str, Dict[str, ProductInfo]]) -> List[MatchCandidate]:
    """解析泡泡玛特特有的系列表达式"""
    matches = []
    
    # 1. 处理中括号衍生品表达：DIMOO心动特调系列-衍生品【软脸毛绒钥匙扣盲盒/水晶球/咖啡杯】
    bracket_pattern = re.compile(r'([^【]+?)(?:系列)?-?衍生品?【([^】]+)】')
    for match in bracket_pattern.finditer(text):
        series_prefix = match.group(1).strip()
        items_str = match.group(2)
        
        # 标准化系列前缀
        series_norm = normalize(series_prefix)
        if "系列" not in series_prefix:
            series_norm = normalize(series_prefix + "系列")
        
        # 分割衍生品项目
        items = re.split(r'[/／、，,]', items_str)
        
        for item in items:
            item = item.strip()
            if not item:
                continue
            
            # 尝试匹配具体的子商品
            item_norm = normalize(item)
            
            # 在系列映射中查找
            if series_norm in series_map:
                for variant_key, product in series_map[series_norm].items():
                    # 检查子商品名称是否包含该项目
                    if item_norm in normalize(product.name) or any(item_norm in normalize(pattern) for pattern in product.patterns):
                        matches.append(MatchCandidate(
                            product_info=product,
                            pattern=f"{series_prefix}衍生品{item}",
                            start=match.start(),
                            end=match.end(),
                            match_type='series_expand'
                        ))
    
    # 2. 处理简单的系列-子商品表达：DIMOO心动特调系列-软脸毛绒钥匙扣盲盒
    series_item_pattern = re.compile(r'([^-]+系列)-([^，。；！？\n]+)')
    for match in series_item_pattern.finditer(text):
        series_name = match.group(1).strip()
        item_name = match.group(2).strip()
        
        series_norm = normalize(series_name)
        item_norm = normalize(item_name)
        
        # 在系列映射中查找匹配的子商品
        if series_norm in series_map:
            for variant_key, product in series_map[series_norm].items():
                # 检查是否匹配
                if item_norm in normalize(product.name):
                    matches.append(MatchCandidate(
                        product_info=product,
                        pattern=f"{series_name}-{item_name}",
                        start=match.start(),
                        end=match.end(),
                        match_type='series_expand'
                    ))
    
    # 3. 处理分号分隔的多个商品：迪士尼公主创想世界系列手办；KUBO日落之城系列手办；浪漫指尖系列4场景手办
    semicolon_pattern = re.compile(r'([^；;]+?)(?:；|;|$)')
    for match in semicolon_pattern.finditer(text):
        item_text = match.group(1).strip()
        if not item_text:
            continue
        
        item_norm = normalize(item_text)
        
        # 直接在所有商品中查找匹配
        for series_key, variants in series_map.items():
            for variant_key, product in variants.items():
                if item_norm == normalize(product.name) or any(item_norm == normalize(pattern) for pattern in product.patterns):
                    matches.append(MatchCandidate(
                        product_info=product,
                        pattern=item_text,
                        start=match.start(),
                        end=match.start() + len(item_text),
                        match_type='series_expand'
                    ))
    
    return matches

# ------------------------------
# 泡泡玛特商品匹配器
# ------------------------------

class PopmartProductMatcher:
    """泡泡玛特专用商品匹配器"""
    
    def __init__(self, product_catalog: List[Dict[str, Any]]):
        """
        初始化匹配器
        
        Args:
            product_catalog: 商品目录，格式：
            [
                {
                    "id": 1,
                    "name": "迪士尼公主创想世界系列手办",
                    "series": "迪士尼公主创想世界系列",
                    "sub_product": None  # 主系列商品
                },
                {
                    "id": 5,
                    "name": "DIMOO心动特调系列-搪胶毛绒吊卡",
                    "series": "DIMOO心动特调系列", 
                    "sub_product": "搪胶毛绒吊卡"
                },
                ...
            ]
        """
        self.products: List[ProductInfo] = []
        self.exact_trie = Trie()
        self.series_map: Dict[str, Dict[str, ProductInfo]] = defaultdict(dict)
        self.name_to_product: Dict[str, ProductInfo] = {}
        self._build_index(product_catalog)
    
    def _build_index(self, product_catalog: List[Dict[str, Any]]):
        """构建索引"""
        for item in product_catalog:
            product_id = int(item.get('id', 0))
            name = item.get('name', '')
            series = item.get('series', '')
            sub_product = item.get('sub_product', '')
            
            if not name or not product_id:
                continue
            
            # 生成匹配模式
            patterns = [normalize(name)]
            
            # 添加系列相关的模式
            if series:
                patterns.append(normalize(series))
                if sub_product:
                    patterns.append(normalize(f"{series}-{sub_product}"))
                    patterns.append(normalize(sub_product))
            
            # 去重
            patterns = list(set(p for p in patterns if p))
            
            product = ProductInfo(
                product_id=product_id,
                name=name,
                patterns=patterns,
                series_key=series if series else None,
                series_variant=sub_product if sub_product else None
            )
            
            self.products.append(product)
            self.name_to_product[normalize(name)] = product
            
            # 构建精确匹配Trie
            for pattern in patterns:
                self.exact_trie.insert(pattern, product)
            
            # 构建系列映射
            if series:
                series_norm = normalize(series)
                variant_key = normalize(sub_product) if sub_product else "main"
                self.series_map[series_norm][variant_key] = product
    
    def match(self, text: str, max_edits: int = 1) -> List[Dict[str, Any]]:
        """
        匹配文本中的商品
        
        Returns:
            List[{
                "商品ID": int,
                "商品名称": str,
                "匹配信息": {
                    "匹配模式": str,
                    "原始文本中的位置": [start, end],
                    "错字信息列表": [错字位置...]
                }
            }]
        """
        text_norm, idx_map = normalize_with_map(text)
        
        # 1. 精确匹配
        exact_matches = self.exact_trie.search_all_matches(text_norm)
        
        # 2. 模糊匹配（仅在max_edits > 0时）
        fuzzy_matches = []
        if max_edits > 0:
            fuzzy_matches = self._fuzzy_match(text_norm, max_edits)
        
        # 3. 泡泡玛特特有的系列表达式匹配
        series_matches = _parse_popmart_series_expressions(text, self.series_map)
        
        # 4. 合并所有候选并去重
        all_candidates = exact_matches + fuzzy_matches + series_matches
        selected = self._select_non_overlapping(all_candidates)
        
        # 5. 构建返回结果
        results = []
        for candidate in selected:
            # 计算原始文本位置
            if candidate.match_type == 'series_expand':
                orig_start, orig_end = candidate.start, candidate.end
            else:
                orig_start, orig_end = self._span_to_original(
                    candidate.start, candidate.end, idx_map
                )
            
            # 计算错字信息
            typos = []
            if candidate.match_type == 'fuzzy' and candidate.edits > 0:
                seg_norm = text_norm[candidate.start:candidate.end]
                typos = self._calc_typos(candidate.pattern, seg_norm, candidate.start, idx_map)
            
            result = {
                "商品ID": candidate.product_info.product_id,
                "商品名称": candidate.product_info.name,
                "匹配信息": {
                    "匹配模式": candidate.pattern,
                    "原始文本中的位置": [orig_start, orig_end],
                    "错字信息列表": typos
                }
            }
            results.append(result)
        
        return results

    def _fuzzy_match(self, text_norm: str, max_edits: int) -> List[MatchCandidate]:
        """模糊匹配"""
        matches = []
        text_len = len(text_norm)

        for product in self.products:
            for pattern in product.patterns:
                pattern_len = len(pattern)
                win_min = max(1, pattern_len - max_edits)
                win_max = pattern_len + max_edits

                for length in range(win_min, min(win_max + 1, text_len + 1)):
                    for start in range(text_len - length + 1):
                        segment = text_norm[start:start + length]
                        dist = _bounded_levenshtein(pattern, segment, max_edits)
                        if dist is not None:
                            matches.append(MatchCandidate(
                                product_info=product,
                                pattern=pattern,
                                start=start,
                                end=start + length,
                                match_type='fuzzy',
                                edits=dist
                            ))

        return matches

    def _select_non_overlapping(self, candidates: List[MatchCandidate]) -> List[MatchCandidate]:
        """选择不重叠的候选，优先级：长度 > 精确匹配 > 编辑距离小 > 位置靠前"""
        def priority_key(c: MatchCandidate):
            return (
                -c.length,  # 长度优先（降序）
                0 if c.match_type == 'exact' else 1,  # 精确匹配优先
                c.edits,  # 编辑距离小优先
                c.start,  # 位置靠前优先
            )

        candidates_sorted = sorted(candidates, key=priority_key)
        selected = []
        occupied_spans = []

        for candidate in candidates_sorted:
            span = (candidate.start, candidate.end)
            # 检查是否与已选择的候选重叠
            overlapped = any(
                not (span[1] <= occ[0] or occ[1] <= span[0])
                for occ in occupied_spans
            )
            if not overlapped:
                selected.append(candidate)
                occupied_spans.append(span)

        return selected

    def _span_to_original(self, start: int, end: int, idx_map: List[int]) -> Tuple[int, int]:
        """将规范化文本位置转换为原始文本位置"""
        if not idx_map or start >= len(idx_map):
            return (0, 0)

        orig_start = idx_map[start]
        orig_end = (idx_map[end - 1] + 1) if end > start and (end - 1) < len(idx_map) else orig_start
        return (orig_start, orig_end)

    def _calc_typos(self, pattern: str, segment: str, seg_start: int, idx_map: List[int]) -> List[int]:
        """计算错字位置（简化版，返回原始文本中的错字位置）"""
        typos = []
        lp, ls = len(pattern), len(segment)

        # 构建DP表
        dp = [[0] * (ls + 1) for _ in range(lp + 1)]
        for i in range(lp + 1):
            dp[i][0] = i
        for j in range(ls + 1):
            dp[0][j] = j

        for i in range(1, lp + 1):
            for j in range(1, ls + 1):
                cost = 0 if pattern[i-1] == segment[j-1] else 1
                dp[i][j] = min(
                    dp[i-1][j] + 1,      # 删除
                    dp[i][j-1] + 1,      # 插入
                    dp[i-1][j-1] + cost  # 替换
                )

        # 简单回溯找错字位置
        i, j = lp, ls
        while i > 0 and j > 0:
            if pattern[i-1] != segment[j-1] and dp[i][j] == dp[i-1][j-1] + 1:
                # 替换操作，记录原始文本位置
                orig_pos = idx_map[seg_start + j - 1] if (seg_start + j - 1) < len(idx_map) else None
                if orig_pos is not None:
                    typos.append(orig_pos)
            i -= 1
            j -= 1

        return sorted(typos)


# ------------------------------
# 数据转换工具
# ------------------------------

def convert_popmart_data(raw_products: List[str]) -> List[Dict[str, Any]]:
    """
    将你提供的商品数据格式转换为匹配器所需格式

    Args:
        raw_products: 原始商品列表，格式如：
        [
            "迪士尼公主创想世界系列手办",
            "DIMOO心动特调系列-搪胶毛绒吊卡，DIMOO心动特调系列",
            "DIMOO心动特调系列-软脸毛绒钥匙扣盲盒，DIMOO心动特调系列，钥匙扣",
            ...
        ]

    Returns:
        转换后的商品目录
    """
    products = []
    product_id = 1

    for raw in raw_products:
        parts = [p.strip() for p in raw.split('，') if p.strip()]
        if not parts:
            continue

        name = parts[0]  # 商品名称
        series = parts[1] if len(parts) > 1 else ""  # 系列名称
        sub_product = parts[2] if len(parts) > 2 else ""  # 子商品类型

        # 如果商品名称包含系列信息，提取子商品
        if series and '-' in name and series in name:
            sub_part = name.replace(series, '').replace('-', '').strip()
            if sub_part and not sub_product:
                sub_product = sub_part

        product = {
            "id": product_id,
            "name": name,
            "series": series,
            "sub_product": sub_product
        }

        products.append(product)
        product_id += 1

    return products


# ------------------------------
# 全局匹配器实例
# ------------------------------

_global_popmart_matcher: Optional[PopmartProductMatcher] = None

def set_popmart_matcher(product_catalog: List[Dict[str, Any]]):
    """设置全局泡泡玛特匹配器"""
    global _global_popmart_matcher
    _global_popmart_matcher = PopmartProductMatcher(product_catalog)

def get_popmart_matcher() -> Optional[PopmartProductMatcher]:
    """获取全局泡泡玛特匹配器"""
    return _global_popmart_matcher

def match_popmart_products(text: str, max_edits: int = 1) -> List[Dict[str, Any]]:
    """
    使用全局泡泡玛特匹配器进行匹配

    Args:
        text: 待匹配文本
        max_edits: 最大编辑距离

    Returns:
        匹配结果列表
    """
    matcher = get_popmart_matcher()
    if matcher is None:
        return []
    return matcher.match(text, max_edits)


__all__ = [
    'PopmartProductMatcher',
    'convert_popmart_data',
    'set_popmart_matcher',
    'get_popmart_matcher',
    'match_popmart_products',
]
