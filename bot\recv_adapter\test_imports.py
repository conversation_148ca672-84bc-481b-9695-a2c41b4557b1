"""
测试所有导入是否正常
"""
import sys
import traceback

def test_imports():
    """测试所有关键模块的导入"""
    print("Testing imports...")
    
    try:
        print("1. Testing utils.mq_publisher...")
        from utils.mq_publisher import ProductMatchPublisher, publish_product_matches
        print("   ✅ utils.mq_publisher imported successfully")
        
        print("2. Testing handlers.message_handler...")
        from handlers.message_handler import MessageHandler
        print("   ✅ handlers.message_handler imported successfully")
        
        print("3. Testing main module...")
        from main import WeChatBotReceiver
        print("   ✅ main module imported successfully")
        
        print("4. Testing config...")
        from config import settings
        print("   ✅ config imported successfully")
        
        print("\n🎉 All imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        traceback.print_exc()
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)
