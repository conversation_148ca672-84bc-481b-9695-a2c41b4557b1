"""
商品匹配器使用示例

展示如何使用高性能商品匹配器进行商品识别
"""

from product_matcher_v2 import ProductMatcher, set_global_matcher, match_products

def main():
    # 示例商品目录
    product_catalog = [
        {
            "id": 123,
            "name": "ABC",
            "match_names": ["ABC", "A B C", "abc"],
        },
        {
            "id": 124,
            "name": "ABC-A",
            "match_names": ["ABC-A", "ABCA", "abc-a"],
            "series": {"key": "ABC", "variant": "A"}
        },
        {
            "id": 125,
            "name": "ABC-B",
            "match_names": ["ABC-B", "ABCB", "abc-b"],
            "series": {"key": "ABC", "variant": "B"}
        },
        {
            "id": 126,
            "name": "LABUBU毛绒",
            "match_names": ["LABUBU毛绒", "LABUBU plush", "labubu毛绒"],
        },
        {
            "id": 127,
            "name": "骷髅熊猫",
            "match_names": ["骷髅熊猫", "skull panda"],
        },
        {
            "id": 128,
            "name": "12345",
            "match_names": ["12345"],
        },
        {
            "id": 129,
            "name": "123",
            "match_names": ["123"],
        },
    ]

    # 方法1：直接使用匹配器实例
    print("=" * 50)
    print("方法1：直接使用匹配器实例")
    print("=" * 50)
    
    matcher = ProductMatcher(product_catalog)
    
    # 测试文本
    test_texts = [
        "本次发售的商品有ABC\nABC-A\nABC-B",
        "ABC，ABC-A，ABC-B",
        "ABC-A/B",
        "ABC/A/B",
        "ABC及其衍生品A和B",
        "今天有labbu毛绒和骷髅熊猫",
        "文本中包含0123456",  # 测试包含关系
        "有个商品叫labbu毛绒",  # 测试错别字
    ]
    
    for text in test_texts:
        print(f"\n测试文本: {text}")
        results = matcher.match(text, max_edits=1)
        print(f"匹配结果数量: {len(results)}")
        for result in results:
            print(f"  - 商品ID: {result['商品ID']}")
            print(f"    商品名称: {result['商品名称']}")
            print(f"    匹配模式: {result['匹配信息']['匹配模式']}")
            print(f"    原始位置: {result['匹配信息']['原始文本中的位置']}")
            if result['匹配信息']['错字信息列表']:
                print(f"    错字位置: {result['匹配信息']['错字信息列表']}")

    # 方法2：使用全局匹配器
    print("\n" + "=" * 50)
    print("方法2：使用全局匹配器")
    print("=" * 50)
    
    # 设置全局匹配器
    set_global_matcher(product_catalog)
    
    # 使用全局匹配器
    text = "ABC-A/B和LABUBU毛绒"
    print(f"\n测试文本: {text}")
    results = match_products(text, max_edits=1)
    print(f"匹配结果数量: {len(results)}")
    for result in results:
        print(f"  - 商品ID: {result['商品ID']}")
        print(f"    商品名称: {result['商品名称']}")
        print(f"    匹配模式: {result['匹配信息']['匹配模式']}")
        print(f"    原始位置: {result['匹配信息']['原始文本中的位置']}")

    # 方法3：在 MessageHandler 中使用
    print("\n" + "=" * 50)
    print("方法3：在 MessageHandler 中使用")
    print("=" * 50)
    
    print("在 MessageHandler 中设置商品目录:")
    print("```python")
    print("from handlers.message_handler import MessageHandler")
    print("")
    print("# 创建消息处理器")
    print("handler = MessageHandler()")
    print("")
    print("# 设置商品目录")
    print("handler.product_catalog = [")
    print("    {")
    print('        "id": 123,')
    print('        "name": "ABC",')
    print('        "match_names": ["ABC", "A B C"],')
    print("    },")
    print("    {")
    print('        "id": 124,')
    print('        "name": "ABC-A",')
    print('        "match_names": ["ABC-A", "ABCA"],')
    print('        "series": {"key": "ABC", "variant": "A"}')
    print("    },")
    print("    # ... 更多商品")
    print("]")
    print("")
    print("# 之后处理消息时会自动进行商品匹配")
    print("# 匹配结果会放在转发数据的 Data.ProductMatches 字段中")
    print("```")

    print("\n" + "=" * 50)
    print("性能优势说明")
    print("=" * 50)
    print("1. 预构建索引：商品列表固定后，一次性构建Trie树和系列映射")
    print("2. 精确匹配加速：使用Trie树进行O(m)复杂度的精确匹配")
    print("3. 模糊匹配优化：带上界剪枝的编辑距离算法，避免无效计算")
    print("4. 系列表达式：专门处理ABC-A/B、ABC/A/B等复杂表达")
    print("5. 最长优先：自动处理包含关系，确保'0123456'匹配'12345'而非'123'")
    print("6. 错字定位：提供原始文本中的错字位置信息")

if __name__ == "__main__":
    main()
